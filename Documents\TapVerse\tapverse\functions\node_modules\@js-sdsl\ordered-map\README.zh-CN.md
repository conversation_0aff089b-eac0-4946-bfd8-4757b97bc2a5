<p align="center">
  <a href="https://js-sdsl.org/" target="_blank" rel="noopener noreferrer">
    <img src="https://js-sdsl.org/assets/image/logo/logo-removebg.png" alt="js-sdsl logo" width="120" />
  </a>
</p>

<h3><p align="center">一款参考 C++ STL 实现的 JavaScript 标准数据结构库</p></h3>

<p align="center">
  <a href="https://www.npmjs.com/package/js-sdsl"><img src="https://img.shields.io/npm/v/js-sdsl.svg" alt="NPM Version" /></a>
  <a href="https://github.com/js-sdsl/js-sdsl/actions/workflows/build.yml"><img src="https://img.shields.io/github/actions/workflow/status/js-sdsl/js-sdsl/build.yml" alt="Build Status" /></a>
  <a href='https://coveralls.io/github/js-sdsl/js-sdsl?branch=main'><img src='https://coveralls.io/repos/github/js-sdsl/js-sdsl/badge.svg?branch=main' alt='Coverage Status' /></a>
  <a href="https://github.com/js-sdsl/js-sdsl"><img src="https://img.shields.io/github/stars/js-sdsl/js-sdsl.svg" alt="GITHUB Star" /></a>
  <a href="https://npmcharts.com/compare/js-sdsl?minimal=true"><img src="https://img.shields.io/npm/dm/js-sdsl.svg" alt="NPM Downloads" /></a>
  <a href="https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js"><img src="https://img.badgesize.io/https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js?compression=gzip&style=flat-square/" alt="Gzip Size"></a>
  <a href="https://openbase.com/js/js-sdsl?utm_source=embedded&amp;utm_medium=badge&amp;utm_campaign=rate-badge"><img src="https://badges.openbase.com/js/rating/js-sdsl.svg?token=fh3LMNOV+JSWykSjtg1rA8kouSYkJoIDzGbvaByq5X0=" alt="Rate this package"/></a>
  <a href="https://opensource.org/licenses/MIT"><img src="https://img.shields.io/npm/l/js-sdsl.svg" alt="MIT-license" /></a>
  <a href="https://github.com/js-sdsl/js-sdsl/"><img src="https://img.shields.io/github/languages/top/js-sdsl/js-sdsl.svg" alt="GITHUB-language" /></a>
</p>

<p align="center"><a href="https://github.com/js-sdsl/js-sdsl/blob/main/README.md">English</a> | 简体中文</p>

## ✨ 包含的数据结构

- **Stack** - 先进后出的堆栈
- **Queue** - 先进先出的队列
- **PriorityQueue** - 堆实现的优先级队列
- **Vector** - 受保护的数组，不能直接操作像 `length` 这样的属性
- **LinkList** - 非连续内存地址的链表
- **Deque** - 双端队列，向前和向后插入元素或按索引获取元素的时间复杂度为 O(1)
- **OrderedSet** - 由红黑树实现的排序集合
- **OrderedMap** - 由红黑树实现的排序字典
- **HashSet** - 参考 [ES6 Set polyfill](https://github.com/rousan/collections-es6) 实现的哈希集合
- **HashMap** - 参考 [ES6 Set polyfill](https://github.com/rousan/collections-es6) 实现的哈希字典

## ⚔️ 基准测试

我们和其他数据结构库进行了基准测试，在某些场景我们甚至超过了当前最流行的库

查看 [benchmark](https://js-sdsl.org/#/zh-cn/test/benchmark-analyze) 以获取更多信息

## 🖥 支持的平台

| ![][Edge-Icon]<br/>IE / Edge | ![][Firefox-Icon]<br/>Firefox | ![][Chrome-Icon]<br/>Chrome | ![][Safari-Icon]<br/>Safari | ![][Opera-Icon]<br/>Opera | ![][NodeJs-Icon]<br/>NodeJs |
|:----------------------------:|:-----------------------------:|:---------------------------:|:---------------------------:|:-------------------------:|:---------------------------:|
|           Edge 12            |              36               |             49              |             10              |            36             |             10              |

## 📦 下载

使用 cdn 直接引入

- [js-sdsl.js](https://unpkg.com/js-sdsl/dist/umd/js-sdsl.js) (for development)
- [js-sdsl.min.js](https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js) (for production)

使用 npm 下载

```bash
npm install js-sdsl
```

或者根据需要安装以下任意单个包

| package                                           | npm                                                                   | size                                                             | docs                        |
|---------------------------------------------------|-----------------------------------------------------------------------|------------------------------------------------------------------|-----------------------------|
| [@js-sdsl/stack][stack-package]                   | [![NPM Package][stack-npm-version]][stack-npm-link]                   | [![GZIP Size][stack-umd-size]][stack-umd-link]                   | [link][stack-docs]          |
| [@js-sdsl/queue][queue-package]                   | [![NPM Package][queue-npm-version]][queue-npm-link]                   | [![GZIP Size][queue-umd-size]][queue-umd-link]                   | [link][queue-docs]          |
| [@js-sdsl/priority-queue][priority-queue-package] | [![NPM Package][priority-queue-npm-version]][priority-queue-npm-link] | [![GZIP Size][priority-queue-umd-size]][priority-queue-umd-link] | [link][priority-queue-docs] |
| [@js-sdsl/vector][vector-package]                 | [![NPM Package][vector-npm-version]][vector-npm-link]                 | [![GZIP Size][vector-umd-size]][vector-umd-link]                 | [link][vector-docs]         |
| [@js-sdsl/link-list][link-list-package]           | [![NPM Package][link-list-npm-version]][link-list-npm-link]           | [![GZIP Size][link-list-umd-size]][link-list-umd-link]           | [link][link-list-docs]      |
| [@js-sdsl/deque][deque-package]                   | [![NPM Package][deque-npm-version]][deque-npm-link]                   | [![GZIP Size][deque-umd-size]][deque-umd-link]                   | [link][deque-docs]          |
| [@js-sdsl/ordered-set][ordered-set-package]       | [![NPM Package][ordered-set-npm-version]][ordered-set-npm-link]       | [![GZIP Size][ordered-set-umd-size]][ordered-set-umd-link]       | [link][ordered-set-docs]    |
| [@js-sdsl/ordered-map][ordered-map-package]       | [![NPM Package][ordered-map-npm-version]][ordered-map-npm-link]       | [![GZIP Size][ordered-map-umd-size]][ordered-map-umd-link]       | [link][ordered-map-docs]    |
| [@js-sdsl/hash-set][hash-set-package]             | [![NPM Package][hash-set-npm-version]][hash-set-npm-link]             | [![GZIP Size][hash-set-umd-size]][hash-set-umd-link]             | [link][hash-set-docs]       |
| [@js-sdsl/hash-map][hash-map-package]             | [![NPM Package][hash-map-npm-version]][hash-map-npm-link]             | [![GZIP Size][hash-map-umd-size]][hash-map-umd-link]             | [link][hash-map-docs]       |

## 🪒 使用说明

您可以[访问我们的主页](https://js-sdsl.org/)获取更多信息

并且我们提供了完整的 [API 文档](https://js-sdsl.org/js-sdsl/index.html)供您参考

想要查看从前版本的文档，请访问：

`https://js-sdsl.org/js-sdsl/previous/v${version}/index.html`

例如：

[https://js-sdsl.org/js-sdsl/previous/v4.1.5/index.html](https://js-sdsl.org/js-sdsl/previous/v4.1.5/index.html)

### 在浏览器中使用

```html
<script src="https://unpkg.com/js-sdsl/dist/umd/js-sdsl.min.js"></script>
<script>
    const { 
      Vector,
      Stack,
      Queue,
      LinkList,
      Deque,
      PriorityQueue,
      OrderedSet,
      OrderedMap,
      HashSet,
      HashMap
    } = sdsl;
    const myOrderedMap = new OrderedMap();
    myOrderedMap.setElement(1, 2);
    console.log(myOrderedMap.getElementByKey(1)); // 2
</script>
```

### npm 引入

```javascript
// esModule
import { OrderedMap } from 'js-sdsl';
// commonJs
const { OrderedMap } = require('js-sdsl');
const myOrderedMap = new OrderedMap();
myOrderedMap.setElement(1, 2);
console.log(myOrderedMap.getElementByKey(1)); // 2
```

## 🛠 测试

### 单元测试

我们使用 [karma](https://karma-runner.github.io/) 和 [mocha](https://mochajs.org/) 框架进行单元测试，并同步到 [coveralls](https://coveralls.io/github/js-sdsl/js-sdsl) 上，你可以使用 `yarn test:unit` 命令来重建它

### 对于性能的校验

我们对于编写的所有 API 进行了性能测试，并将结果同步到了 [`gh-pages/performance.md`](https://github.com/js-sdsl/js-sdsl/blob/gh-pages/performance.md) 中，你可以通过 `yarn test:performance` 命令来重现它

您也可以访问[我们的网站](https://js-sdsl.org/#/zh-cn/test/performance-test)来获取结果

## ⌨️ 开发

可以使用 Gitpod 进行在线编辑：

[![Open in Gippod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/js-sdsl/js-sdsl)

或者在本地使用以下命令获取源码进行开发：

```bash
$ git clone https://github.com/js-sdsl/js-sdsl.git
$ cd js-sdsl
$ npm install
$ npm run dev   # development mode
```

之后您在 `dist/cjs` 文件夹中可以看到在 `dev` 模式下打包生成的产物

## 🤝 贡献

我们欢迎所有的开发人员提交 issue 或 pull request，阅读[贡献者指南](https://github.com/js-sdsl/js-sdsl/blob/main/.github/CONTRIBUTING.md)可能会有所帮助

### 贡献者

感谢对本项目做出贡献的开发者们：

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center"><a href="https://www.linkedin.com/in/takatoshi-kondo-02a91410/"><img src="https://avatars.githubusercontent.com/u/275959?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Takatoshi Kondo</b></sub></a><br /><a href="https://github.com/js-sdsl/js-sdsl/commits?author=redboltz" title="Code">💻</a> <a href="https://github.com/js-sdsl/js-sdsl/commits?author=redboltz" title="Tests">⚠️</a></td>
      <td align="center"><a href="https://www.youtube.com/c/noname0310"><img src="https://avatars.githubusercontent.com/u/48761044?v=4?s=100" width="100px;" alt=""/><br /><sub><b>noname</b></sub></a><br /><a href="https://github.com/js-sdsl/js-sdsl/commits?author=noname0310" title="Code">💻</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

本项目遵循 [all-contributors](https://github.com/all-contributors/all-contributors) 规范。 欢迎任何形式的贡献！

## ❤️ 赞助者

特别鸣谢下列赞助商和支持者们，他们在非常早期的时候为我们提供了支持：

<a href="https://eslint.org/"><img src="https://js-sdsl.org/assets/image/sponsors/eslint-logo-color.png" alt="eslint logo" width="150"></a>

同样感谢这些赞助商和支持者们：

[![sponsors](https://opencollective.com/js-sdsl/tiers/sponsors.svg?avatarHeight=36)](https://opencollective.com/js-sdsl#support)

[![backers](https://opencollective.com/js-sdsl/tiers/backers.svg?avatarHeight=36)](https://opencollective.com/js-sdsl#support)

## 🪪 许可证

[MIT](https://github.com/js-sdsl/js-sdsl/blob/main/LICENSE) © [ZLY201](https://github.com/zly201)

[Edge-Icon]: https://js-sdsl.org/assets/image/platform/edge.png
[Firefox-Icon]: https://js-sdsl.org/assets/image/platform/firefox.png
[Chrome-Icon]: https://js-sdsl.org/assets/image/platform/chrome.png
[Safari-Icon]: https://js-sdsl.org/assets/image/platform/safari.png
[Opera-Icon]: https://js-sdsl.org/assets/image/platform/opera.png
[NodeJs-Icon]: https://js-sdsl.org/assets/image/platform/nodejs.png

[stack-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/OtherContainer/Stack.ts
[stack-npm-version]: https://img.shields.io/npm/v/@js-sdsl/stack
[stack-npm-link]: https://www.npmjs.com/package/@js-sdsl/stack
[stack-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/stack/dist/umd/stack.min.js?compression=gzip&style=flat-square/
[stack-umd-link]: https://unpkg.com/@js-sdsl/stack/dist/umd/stack.min.js
[stack-docs]: https://js-sdsl.org/js-sdsl/classes/Stack.html

[queue-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/OtherContainer/Queue.ts
[queue-npm-version]: https://img.shields.io/npm/v/@js-sdsl/queue
[queue-npm-link]: https://www.npmjs.com/package/@js-sdsl/queue
[queue-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/queue/dist/umd/queue.min.js?compression=gzip&style=flat-square/
[queue-umd-link]: https://unpkg.com/@js-sdsl/queue/dist/umd/queue.min.js
[queue-docs]: https://js-sdsl.org/js-sdsl/classes/Queue.html

[priority-queue-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/OtherContainer/PriorityQueue.ts
[priority-queue-npm-version]: https://img.shields.io/npm/v/@js-sdsl/priority-queue
[priority-queue-npm-link]: https://www.npmjs.com/package/@js-sdsl/priority-queue
[priority-queue-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/priority-queue/dist/umd/priority-queue.min.js?compression=gzip&style=flat-square/
[priority-queue-umd-link]: https://unpkg.com/@js-sdsl/priority-queue/dist/umd/priority-queue.min.js
[priority-queue-docs]: https://js-sdsl.org/js-sdsl/classes/PriorityQueue.html

[vector-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/SequentialContainer/Vector.ts
[vector-npm-version]: https://img.shields.io/npm/v/@js-sdsl/vector
[vector-npm-link]: https://www.npmjs.com/package/@js-sdsl/vector
[vector-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/vector/dist/umd/vector.min.js?compression=gzip&style=flat-square/
[vector-umd-link]: https://unpkg.com/@js-sdsl/vector/dist/umd/vector.min.js
[vector-docs]: https://js-sdsl.org/js-sdsl/classes/Vector.html

[link-list-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/SequentialContainer/LinkList.ts
[link-list-npm-version]: https://img.shields.io/npm/v/@js-sdsl/link-list
[link-list-npm-link]: https://www.npmjs.com/package/@js-sdsl/link-list
[link-list-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/link-list/dist/umd/link-list.min.js?compression=gzip&style=flat-square/
[link-list-umd-link]: https://unpkg.com/@js-sdsl/link-list/dist/umd/link-list.min.js
[link-list-docs]: https://js-sdsl.org/js-sdsl/classes/LinkList.html

[deque-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/SequentialContainer/Deque.ts
[deque-npm-version]: https://img.shields.io/npm/v/@js-sdsl/deque
[deque-npm-link]: https://www.npmjs.com/package/@js-sdsl/deque
[deque-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/deque/dist/umd/deque.min.js?compression=gzip&style=flat-square/
[deque-umd-link]: https://unpkg.com/@js-sdsl/deque/dist/umd/deque.min.js
[deque-docs]: https://js-sdsl.org/js-sdsl/classes/Deque.html

[ordered-set-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/TreeContainer/OrderedSet.ts
[ordered-set-npm-version]: https://img.shields.io/npm/v/@js-sdsl/ordered-set
[ordered-set-npm-link]: https://www.npmjs.com/package/@js-sdsl/ordered-set
[ordered-set-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/ordered-set/dist/umd/ordered-set.min.js?compression=gzip&style=flat-square/
[ordered-set-umd-link]: https://unpkg.com/@js-sdsl/ordered-set/dist/umd/ordered-set.min.js
[ordered-set-docs]: https://js-sdsl.org/js-sdsl/classes/OrderedSet.html

[ordered-map-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/TreeContainer/OrderedMap.ts
[ordered-map-npm-version]: https://img.shields.io/npm/v/@js-sdsl/ordered-map
[ordered-map-npm-link]: https://www.npmjs.com/package/@js-sdsl/ordered-map
[ordered-map-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/ordered-map/dist/umd/ordered-map.min.js?compression=gzip&style=flat-square/
[ordered-map-umd-link]: https://unpkg.com/@js-sdsl/ordered-map/dist/umd/ordered-map.min.js
[ordered-map-docs]: https://js-sdsl.org/js-sdsl/classes/OrderedMap.html

[hash-set-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/HashContainer/HashSet.ts
[hash-set-npm-version]: https://img.shields.io/npm/v/@js-sdsl/hash-set
[hash-set-npm-link]: https://www.npmjs.com/package/@js-sdsl/hash-set
[hash-set-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/hash-set/dist/umd/hash-set.min.js?compression=gzip&style=flat-square/
[hash-set-umd-link]: https://unpkg.com/@js-sdsl/hash-set/dist/umd/hash-set.min.js
[hash-set-docs]: https://js-sdsl.org/js-sdsl/classes/HashSet.html

[hash-map-package]: https://github.com/js-sdsl/js-sdsl/blob/main/src/container/HashContainer/HashMap.ts
[hash-map-npm-version]: https://img.shields.io/npm/v/@js-sdsl/hash-map
[hash-map-npm-link]: https://www.npmjs.com/package/@js-sdsl/hash-map
[hash-map-umd-size]: https://img.badgesize.io/https://unpkg.com/@js-sdsl/hash-map/dist/umd/hash-map.min.js?compression=gzip&style=flat-square/
[hash-map-umd-link]: https://unpkg.com/@js-sdsl/hash-map/dist/umd/hash-map.min.js
[hash-map-docs]: https://js-sdsl.org/js-sdsl/classes/HashMap.html
