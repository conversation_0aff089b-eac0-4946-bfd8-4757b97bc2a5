"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenBalance = exports.claimDailyReward = exports.openMysteryBox = exports.awardGameTokens = exports.activateBoost = exports.purchaseItem = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
admin.initializeApp();
const db = admin.firestore();
// Purchase Item Function
exports.purchaseItem = functions.https.onCall(async (data, context) => {
    // Verify authentication
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId, itemId, itemType, cost } = data;
    if (!userId || !itemId || !itemType || !cost) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }
    if (context.auth.uid !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'User can only purchase for themselves');
    }
    try {
        return await db.runTransaction(async (transaction) => {
            // Check if user already owns this item
            const ownedItemRef = db.collection('users').doc(userId).collection('ownedItems').doc(itemId);
            const ownedItemDoc = await transaction.get(ownedItemRef);
            if (ownedItemDoc.exists) {
                throw new functions.https.HttpsError('already-exists', 'User already owns this item');
            }
            // Get user's current token balance
            const userRef = db.collection('users').doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User not found');
            }
            const userData = userDoc.data();
            const currentTokens = (userData === null || userData === void 0 ? void 0 : userData.tokens) || 0;
            if (currentTokens < cost) {
                throw new functions.https.HttpsError('failed-precondition', 'Insufficient tokens');
            }
            // Deduct tokens
            transaction.update(userRef, {
                tokens: currentTokens - cost,
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
            // Add item to owned items
            transaction.set(ownedItemRef, {
                itemId,
                itemType,
                purchasedAt: admin.firestore.FieldValue.serverTimestamp(),
                cost
            });
            // Log purchase history
            const purchaseRef = db.collection('users').doc(userId).collection('purchaseHistory').doc();
            transaction.set(purchaseRef, {
                itemId,
                itemType,
                cost,
                purchasedAt: admin.firestore.FieldValue.serverTimestamp()
            });
            return {
                success: true,
                newTokenBalance: currentTokens - cost,
                itemId,
                itemType
            };
        });
    }
    catch (error) {
        console.error('Purchase error:', error);
        throw new functions.https.HttpsError('internal', 'Purchase failed');
    }
});
// Activate Boost Function
exports.activateBoost = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId, boostId, duration } = data;
    if (context.auth.uid !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'User can only activate boosts for themselves');
    }
    try {
        return await db.runTransaction(async (transaction) => {
            var _a;
            // Check if user owns this boost
            const ownedBoostRef = db.collection('users').doc(userId).collection('ownedItems').doc(boostId);
            const ownedBoostDoc = await transaction.get(ownedBoostRef);
            if (!ownedBoostDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User does not own this boost');
            }
            // Check if there's already an active boost
            const activeBoostRef = db.collection('users').doc(userId).collection('boosts').doc('activeBoost');
            const activeBoostDoc = await transaction.get(activeBoostRef);
            if (activeBoostDoc.exists) {
                const activeBoostData = activeBoostDoc.data();
                const expiresAt = (_a = activeBoostData === null || activeBoostData === void 0 ? void 0 : activeBoostData.expiresAt) === null || _a === void 0 ? void 0 : _a.toDate();
                if (expiresAt && expiresAt > new Date()) {
                    throw new functions.https.HttpsError('failed-precondition', 'Another boost is already active');
                }
            }
            // Activate the boost
            const expiresAt = new Date(Date.now() + duration * 60 * 1000); // duration in minutes
            transaction.set(activeBoostRef, {
                boostId,
                activatedAt: admin.firestore.FieldValue.serverTimestamp(),
                expiresAt: admin.firestore.Timestamp.fromDate(expiresAt),
                duration
            });
            return {
                success: true,
                boostId,
                expiresAt: expiresAt.toISOString()
            };
        });
    }
    catch (error) {
        console.error('Boost activation error:', error);
        throw new functions.https.HttpsError('internal', 'Boost activation failed');
    }
});
// Award Game Tokens Function
exports.awardGameTokens = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId, gameId, score, tokensEarned } = data;
    if (context.auth.uid !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'User can only earn tokens for themselves');
    }
    try {
        return await db.runTransaction(async (transaction) => {
            const userRef = db.collection('users').doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                // Create user document if it doesn't exist
                transaction.set(userRef, {
                    tokens: tokensEarned,
                    createdAt: admin.firestore.FieldValue.serverTimestamp(),
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                });
            }
            else {
                const userData = userDoc.data();
                const currentTokens = (userData === null || userData === void 0 ? void 0 : userData.tokens) || 0;
                transaction.update(userRef, {
                    tokens: currentTokens + tokensEarned,
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                });
            }
            // Update leaderboard
            const leaderboardRef = db.collection('leaderboards').doc(gameId);
            transaction.set(leaderboardRef, {
                uid: userId,
                score,
                timestamp: admin.firestore.FieldValue.serverTimestamp()
            }, { merge: true });
            return {
                success: true,
                tokensEarned,
                gameId,
                score
            };
        });
    }
    catch (error) {
        console.error('Token award error:', error);
        throw new functions.https.HttpsError('internal', 'Token award failed');
    }
});
// Mystery Box Function
exports.openMysteryBox = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId, boxType } = data;
    if (context.auth.uid !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'User can only open boxes for themselves');
    }
    // Define mystery box costs and possible rewards
    const boxConfig = {
        basic: { cost: 100, rewards: ['skin_1', 'skin_2', 'flair_1', 'tokens_50'] },
        premium: { cost: 250, rewards: ['skin_3', 'skin_4', 'flair_2', 'boost_1', 'tokens_100'] },
        legendary: { cost: 500, rewards: ['skin_5', 'flair_3', 'boost_2', 'tokens_200'] }
    };
    const config = boxConfig[boxType];
    if (!config) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid box type');
    }
    try {
        return await db.runTransaction(async (transaction) => {
            const userRef = db.collection('users').doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User not found');
            }
            const userData = userDoc.data();
            const currentTokens = (userData === null || userData === void 0 ? void 0 : userData.tokens) || 0;
            if (currentTokens < config.cost) {
                throw new functions.https.HttpsError('failed-precondition', 'Insufficient tokens');
            }
            // Randomly select a reward
            const randomReward = config.rewards[Math.floor(Math.random() * config.rewards.length)];
            // Deduct tokens
            transaction.update(userRef, {
                tokens: currentTokens - config.cost,
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });
            // Award the prize
            if (randomReward.startsWith('tokens_')) {
                const tokenAmount = parseInt(randomReward.split('_')[1]);
                transaction.update(userRef, {
                    tokens: currentTokens - config.cost + tokenAmount
                });
            }
            else {
                // Add item to owned items
                const ownedItemRef = db.collection('users').doc(userId).collection('ownedItems').doc(randomReward);
                transaction.set(ownedItemRef, {
                    itemId: randomReward,
                    itemType: randomReward.startsWith('skin') ? 'skin' : randomReward.startsWith('flair') ? 'flair' : 'boost',
                    obtainedFrom: 'mysteryBox',
                    obtainedAt: admin.firestore.FieldValue.serverTimestamp()
                });
            }
            return {
                success: true,
                reward: randomReward,
                boxType,
                newTokenBalance: randomReward.startsWith('tokens_')
                    ? currentTokens - config.cost + parseInt(randomReward.split('_')[1])
                    : currentTokens - config.cost
            };
        });
    }
    catch (error) {
        console.error('Mystery box error:', error);
        throw new functions.https.HttpsError('internal', 'Mystery box opening failed');
    }
});
// Daily Reward Function
exports.claimDailyReward = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId } = data;
    if (context.auth.uid !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'User can only claim rewards for themselves');
    }
    try {
        return await db.runTransaction(async (transaction) => {
            var _a, _b, _c;
            const dailyRewardRef = db.collection('users').doc(userId).collection('dailyRewards').doc('current');
            const dailyRewardDoc = await transaction.get(dailyRewardRef);
            const today = new Date().toDateString();
            if (dailyRewardDoc.exists) {
                const lastClaimed = (_a = dailyRewardDoc.data()) === null || _a === void 0 ? void 0 : _a.lastClaimed;
                if (lastClaimed && new Date(lastClaimed).toDateString() === today) {
                    throw new functions.https.HttpsError('failed-precondition', 'Daily reward already claimed today');
                }
            }
            // Award daily tokens (50 base + streak bonus)
            const streak = dailyRewardDoc.exists ? (((_b = dailyRewardDoc.data()) === null || _b === void 0 ? void 0 : _b.streak) || 0) + 1 : 1;
            const baseReward = 50;
            const streakBonus = Math.min(streak * 5, 50); // Max 50 bonus tokens
            const totalReward = baseReward + streakBonus;
            // Update user tokens
            const userRef = db.collection('users').doc(userId);
            const userDoc = await transaction.get(userRef);
            const currentTokens = userDoc.exists ? (((_c = userDoc.data()) === null || _c === void 0 ? void 0 : _c.tokens) || 0) : 0;
            transaction.set(userRef, {
                tokens: currentTokens + totalReward,
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
            }, { merge: true });
            // Update daily reward record
            transaction.set(dailyRewardRef, {
                lastClaimed: today,
                streak,
                totalReward,
                claimedAt: admin.firestore.FieldValue.serverTimestamp()
            });
            return {
                success: true,
                tokensAwarded: totalReward,
                streak,
                newTokenBalance: currentTokens + totalReward
            };
        });
    }
    catch (error) {
        console.error('Daily reward error:', error);
        throw new functions.https.HttpsError('internal', 'Daily reward claim failed');
    }
});
// Get Token Balance Function
exports.getTokenBalance = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { userId } = data;
    if (context.auth.uid !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'User can only check their own balance');
    }
    try {
        const userRef = db.collection('users').doc(userId);
        const userDoc = await userRef.get();
        if (!userDoc.exists) {
            return {
                success: true,
                tokens: 0
            };
        }
        const userData = userDoc.data();
        return {
            success: true,
            tokens: (userData === null || userData === void 0 ? void 0 : userData.tokens) || 0
        };
    }
    catch (error) {
        console.error('Get token balance error:', error);
        throw new functions.https.HttpsError('internal', 'Failed to get token balance');
    }
});
//# sourceMappingURL=index.js.map