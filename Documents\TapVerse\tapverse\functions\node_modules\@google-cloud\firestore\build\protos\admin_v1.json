{"options": {"syntax": "proto3"}, "nested": {"google": {"nested": {"firestore": {"nested": {"admin": {"nested": {"v1": {"options": {"csharp_namespace": "Google.Cloud.Firestore.Admin.V1", "go_package": "cloud.google.com/go/firestore/apiv1/admin/adminpb;adminpb", "java_multiple_files": true, "java_outer_classname": "LocationProto", "java_package": "com.google.firestore.admin.v1", "objc_class_prefix": "GCFS", "php_namespace": "Google\\Cloud\\Firestore\\Admin\\V1", "ruby_package": "Google::Cloud::Firestore::Admin::V1", "(google.api.resource_definition).type": "firestore.googleapis.com/CollectionGroup", "(google.api.resource_definition).pattern": "projects/{project}/databases/{database}/collectionGroups/{collection}"}, "nested": {"Backup": {"options": {"(google.api.resource).type": "firestore.googleapis.com/Backup", "(google.api.resource).pattern": "projects/{project}/locations/{location}/backups/{backup}"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "database": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "databaseUid": {"type": "string", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "snapshotTime": {"type": "google.protobuf.Timestamp", "id": 3, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "expireTime": {"type": "google.protobuf.Timestamp", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "stats": {"type": "Stats", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "state": {"type": "State", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}, "nested": {"Stats": {"fields": {"sizeBytes": {"type": "int64", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "documentCount": {"type": "int64", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "indexCount": {"type": "int64", "id": 3, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "State": {"values": {"STATE_UNSPECIFIED": 0, "CREATING": 1, "READY": 2, "NOT_AVAILABLE": 3}}}}, "Database": {"options": {"(google.api.resource).type": "firestore.googleapis.com/Database", "(google.api.resource).pattern": "projects/{project}/databases/{database}", "(google.api.resource).style": "DECLARATIVE_FRIENDLY"}, "fields": {"name": {"type": "string", "id": 1}, "uid": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteTime": {"type": "google.protobuf.Timestamp", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "locationId": {"type": "string", "id": 9}, "type": {"type": "DatabaseType", "id": 10}, "concurrencyMode": {"type": "ConcurrencyMode", "id": 15}, "versionRetentionPeriod": {"type": "google.protobuf.Duration", "id": 17, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "earliestVersionTime": {"type": "google.protobuf.Timestamp", "id": 18, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "pointInTimeRecoveryEnablement": {"type": "PointInTimeRecoveryEnablement", "id": 21}, "appEngineIntegrationMode": {"type": "AppEngineIntegrationMode", "id": 19}, "keyPrefix": {"type": "string", "id": 20, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteProtectionState": {"type": "DeleteProtectionState", "id": 22}, "cmekConfig": {"type": "CmekConfig", "id": 23, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "previousId": {"type": "string", "id": 25, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "sourceInfo": {"type": "SourceInfo", "id": 26, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "etag": {"type": "string", "id": 99}}, "nested": {"DatabaseType": {"values": {"DATABASE_TYPE_UNSPECIFIED": 0, "FIRESTORE_NATIVE": 1, "DATASTORE_MODE": 2}}, "ConcurrencyMode": {"values": {"CONCURRENCY_MODE_UNSPECIFIED": 0, "OPTIMISTIC": 1, "PESSIMISTIC": 2, "OPTIMISTIC_WITH_ENTITY_GROUPS": 3}}, "PointInTimeRecoveryEnablement": {"values": {"POINT_IN_TIME_RECOVERY_ENABLEMENT_UNSPECIFIED": 0, "POINT_IN_TIME_RECOVERY_ENABLED": 1, "POINT_IN_TIME_RECOVERY_DISABLED": 2}}, "AppEngineIntegrationMode": {"values": {"APP_ENGINE_INTEGRATION_MODE_UNSPECIFIED": 0, "ENABLED": 1, "DISABLED": 2}}, "DeleteProtectionState": {"values": {"DELETE_PROTECTION_STATE_UNSPECIFIED": 0, "DELETE_PROTECTION_DISABLED": 1, "DELETE_PROTECTION_ENABLED": 2}}, "CmekConfig": {"fields": {"kmsKeyName": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "activeKeyVersion": {"rule": "repeated", "type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SourceInfo": {"oneofs": {"source": {"oneof": ["backup"]}}, "fields": {"backup": {"type": "BackupSource", "id": 1}, "operation": {"type": "string", "id": 3, "options": {"(google.api.resource_reference).type": "firestore.googleapis.com/Operation"}}}, "nested": {"BackupSource": {"fields": {"backup": {"type": "string", "id": 1, "options": {"(google.api.resource_reference).type": "firestore.googleapis.com/Backup"}}}}}}, "EncryptionConfig": {"oneofs": {"encryptionType": {"oneof": ["googleDefaultEncryption", "useSourceEncryption", "customerManagedEncryption"]}}, "fields": {"googleDefaultEncryption": {"type": "GoogleDefaultEncryptionOptions", "id": 1}, "useSourceEncryption": {"type": "SourceEncryptionOptions", "id": 2}, "customerManagedEncryption": {"type": "CustomerManagedEncryptionOptions", "id": 3}}, "nested": {"GoogleDefaultEncryptionOptions": {"fields": {}}, "SourceEncryptionOptions": {"fields": {}}, "CustomerManagedEncryptionOptions": {"fields": {"kmsKeyName": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}}}}}, "Field": {"options": {"(google.api.resource).type": "firestore.googleapis.com/Field", "(google.api.resource).pattern": "projects/{project}/databases/{database}/collectionGroups/{collection}/fields/{field}"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "indexConfig": {"type": "IndexConfig", "id": 2}, "ttlConfig": {"type": "TtlConfig", "id": 3}}, "nested": {"IndexConfig": {"fields": {"indexes": {"rule": "repeated", "type": "Index", "id": 1}, "usesAncestorConfig": {"type": "bool", "id": 2}, "ancestorField": {"type": "string", "id": 3}, "reverting": {"type": "bool", "id": 4}}}, "TtlConfig": {"fields": {"state": {"type": "State", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "CREATING": 1, "ACTIVE": 2, "NEEDS_REPAIR": 3}}}}}}, "Index": {"options": {"(google.api.resource).type": "firestore.googleapis.com/Index", "(google.api.resource).pattern": "projects/{project}/databases/{database}/collectionGroups/{collection}/indexes/{index}"}, "fields": {"name": {"type": "string", "id": 1}, "queryScope": {"type": "QueryScope", "id": 2}, "apiScope": {"type": "ApiScope", "id": 5}, "fields": {"rule": "repeated", "type": "IndexField", "id": 3}, "state": {"type": "State", "id": 4}}, "nested": {"QueryScope": {"values": {"QUERY_SCOPE_UNSPECIFIED": 0, "COLLECTION": 1, "COLLECTION_GROUP": 2, "COLLECTION_RECURSIVE": 3}}, "ApiScope": {"values": {"ANY_API": 0, "DATASTORE_MODE_API": 1}}, "IndexField": {"oneofs": {"valueMode": {"oneof": ["order", "arrayConfig", "vectorConfig"]}}, "fields": {"fieldPath": {"type": "string", "id": 1}, "order": {"type": "Order", "id": 2}, "arrayConfig": {"type": "ArrayConfig", "id": 3}, "vectorConfig": {"type": "VectorConfig", "id": 4}}, "nested": {"Order": {"values": {"ORDER_UNSPECIFIED": 0, "ASCENDING": 1, "DESCENDING": 2}}, "ArrayConfig": {"values": {"ARRAY_CONFIG_UNSPECIFIED": 0, "CONTAINS": 1}}, "VectorConfig": {"oneofs": {"type": {"oneof": ["flat"]}}, "fields": {"dimension": {"type": "int32", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "flat": {"type": "FlatIndex", "id": 2}}, "nested": {"FlatIndex": {"fields": {}}}}}}, "State": {"values": {"STATE_UNSPECIFIED": 0, "CREATING": 1, "READY": 2, "NEEDS_REPAIR": 3}}}}, "FirestoreAdmin": {"options": {"(google.api.default_host)": "firestore.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/datastore"}, "methods": {"CreateIndex": {"requestType": "CreateIndexRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/{parent=projects/*/databases/*/collectionGroups/*}/indexes", "(google.api.http).body": "index", "(google.api.method_signature)": "parent,index", "(google.longrunning.operation_info).response_type": "Index", "(google.longrunning.operation_info).metadata_type": "IndexOperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{parent=projects/*/databases/*/collectionGroups/*}/indexes", "body": "index"}}, {"(google.api.method_signature)": "parent,index"}, {"(google.longrunning.operation_info)": {"response_type": "Index", "metadata_type": "IndexOperationMetadata"}}]}, "ListIndexes": {"requestType": "ListIndexesRequest", "responseType": "ListIndexesResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*/databases/*/collectionGroups/*}/indexes", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*/databases/*/collectionGroups/*}/indexes"}}, {"(google.api.method_signature)": "parent"}]}, "GetIndex": {"requestType": "GetIndexRequest", "responseType": "Index", "options": {"(google.api.http).get": "/v1/{name=projects/*/databases/*/collectionGroups/*/indexes/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/databases/*/collectionGroups/*/indexes/*}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteIndex": {"requestType": "DeleteIndexRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=projects/*/databases/*/collectionGroups/*/indexes/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=projects/*/databases/*/collectionGroups/*/indexes/*}"}}, {"(google.api.method_signature)": "name"}]}, "GetField": {"requestType": "GetFieldRequest", "responseType": "Field", "options": {"(google.api.http).get": "/v1/{name=projects/*/databases/*/collectionGroups/*/fields/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/databases/*/collectionGroups/*/fields/*}"}}, {"(google.api.method_signature)": "name"}]}, "UpdateField": {"requestType": "UpdateFieldRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).patch": "/v1/{field.name=projects/*/databases/*/collectionGroups/*/fields/*}", "(google.api.http).body": "field", "(google.api.method_signature)": "field", "(google.longrunning.operation_info).response_type": "Field", "(google.longrunning.operation_info).metadata_type": "FieldOperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1/{field.name=projects/*/databases/*/collectionGroups/*/fields/*}", "body": "field"}}, {"(google.api.method_signature)": "field"}, {"(google.longrunning.operation_info)": {"response_type": "Field", "metadata_type": "FieldOperationMetadata"}}]}, "ListFields": {"requestType": "ListFieldsRequest", "responseType": "ListFieldsResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*/databases/*/collectionGroups/*}/fields", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*/databases/*/collectionGroups/*}/fields"}}, {"(google.api.method_signature)": "parent"}]}, "ExportDocuments": {"requestType": "ExportDocumentsRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/{name=projects/*/databases/*}:exportDocuments", "(google.api.http).body": "*", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "ExportDocumentsResponse", "(google.longrunning.operation_info).metadata_type": "ExportDocumentsMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{name=projects/*/databases/*}:exportDocuments", "body": "*"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "ExportDocumentsResponse", "metadata_type": "ExportDocumentsMetadata"}}]}, "ImportDocuments": {"requestType": "ImportDocumentsRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/{name=projects/*/databases/*}:importDocuments", "(google.api.http).body": "*", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "google.protobuf.Empty", "(google.longrunning.operation_info).metadata_type": "ImportDocumentsMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{name=projects/*/databases/*}:importDocuments", "body": "*"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "google.protobuf.Empty", "metadata_type": "ImportDocumentsMetadata"}}]}, "BulkDeleteDocuments": {"requestType": "BulkDeleteDocumentsRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/{name=projects/*/databases/*}:bulkDeleteDocuments", "(google.api.http).body": "*", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "BulkDeleteDocumentsResponse", "(google.longrunning.operation_info).metadata_type": "BulkDeleteDocumentsMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{name=projects/*/databases/*}:bulkDeleteDocuments", "body": "*"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "BulkDeleteDocumentsResponse", "metadata_type": "BulkDeleteDocumentsMetadata"}}]}, "CreateDatabase": {"requestType": "CreateDatabaseRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/{parent=projects/*}/databases", "(google.api.http).body": "database", "(google.api.method_signature)": "parent,database,database_id", "(google.longrunning.operation_info).response_type": "Database", "(google.longrunning.operation_info).metadata_type": "CreateDatabaseMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{parent=projects/*}/databases", "body": "database"}}, {"(google.api.method_signature)": "parent,database,database_id"}, {"(google.longrunning.operation_info)": {"response_type": "Database", "metadata_type": "CreateDatabaseMetadata"}}]}, "GetDatabase": {"requestType": "GetDatabaseRequest", "responseType": "Database", "options": {"(google.api.http).get": "/v1/{name=projects/*/databases/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/databases/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListDatabases": {"requestType": "ListDatabasesRequest", "responseType": "ListDatabasesResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*}/databases", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*}/databases"}}, {"(google.api.method_signature)": "parent"}]}, "UpdateDatabase": {"requestType": "UpdateDatabaseRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).patch": "/v1/{database.name=projects/*/databases/*}", "(google.api.http).body": "database", "(google.api.method_signature)": "database,update_mask", "(google.longrunning.operation_info).response_type": "Database", "(google.longrunning.operation_info).metadata_type": "UpdateDatabaseMetadata"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1/{database.name=projects/*/databases/*}", "body": "database"}}, {"(google.api.method_signature)": "database,update_mask"}, {"(google.longrunning.operation_info)": {"response_type": "Database", "metadata_type": "UpdateDatabaseMetadata"}}]}, "DeleteDatabase": {"requestType": "DeleteDatabaseRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v1/{name=projects/*/databases/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "Database", "(google.longrunning.operation_info).metadata_type": "DeleteDatabaseMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=projects/*/databases/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "Database", "metadata_type": "DeleteDatabaseMetadata"}}]}, "GetBackup": {"requestType": "GetBackupRequest", "responseType": "Backup", "options": {"(google.api.http).get": "/v1/{name=projects/*/locations/*/backups/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/locations/*/backups/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListBackups": {"requestType": "ListBackupsRequest", "responseType": "ListBackupsResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*/locations/*}/backups", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*/locations/*}/backups"}}, {"(google.api.method_signature)": "parent"}]}, "DeleteBackup": {"requestType": "DeleteBackupRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=projects/*/locations/*/backups/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=projects/*/locations/*/backups/*}"}}, {"(google.api.method_signature)": "name"}]}, "RestoreDatabase": {"requestType": "RestoreDatabaseRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/{parent=projects/*}/databases:restore", "(google.api.http).body": "*", "(google.longrunning.operation_info).response_type": "Database", "(google.longrunning.operation_info).metadata_type": "RestoreDatabaseMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{parent=projects/*}/databases:restore", "body": "*"}}, {"(google.longrunning.operation_info)": {"response_type": "Database", "metadata_type": "RestoreDatabaseMetadata"}}]}, "CreateBackupSchedule": {"requestType": "CreateBackupScheduleRequest", "responseType": "BackupSchedule", "options": {"(google.api.http).post": "/v1/{parent=projects/*/databases/*}/backupSchedules", "(google.api.http).body": "backup_schedule", "(google.api.method_signature)": "parent,backup_schedule"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{parent=projects/*/databases/*}/backupSchedules", "body": "backup_schedule"}}, {"(google.api.method_signature)": "parent,backup_schedule"}]}, "GetBackupSchedule": {"requestType": "GetBackupScheduleRequest", "responseType": "BackupSchedule", "options": {"(google.api.http).get": "/v1/{name=projects/*/databases/*/backupSchedules/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/databases/*/backupSchedules/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListBackupSchedules": {"requestType": "ListBackupSchedulesRequest", "responseType": "ListBackupSchedulesResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*/databases/*}/backupSchedules", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*/databases/*}/backupSchedules"}}, {"(google.api.method_signature)": "parent"}]}, "UpdateBackupSchedule": {"requestType": "UpdateBackupScheduleRequest", "responseType": "BackupSchedule", "options": {"(google.api.http).patch": "/v1/{backup_schedule.name=projects/*/databases/*/backupSchedules/*}", "(google.api.http).body": "backup_schedule", "(google.api.method_signature)": "backup_schedule,update_mask"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1/{backup_schedule.name=projects/*/databases/*/backupSchedules/*}", "body": "backup_schedule"}}, {"(google.api.method_signature)": "backup_schedule,update_mask"}]}, "DeleteBackupSchedule": {"requestType": "DeleteBackupScheduleRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=projects/*/databases/*/backupSchedules/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=projects/*/databases/*/backupSchedules/*}"}}, {"(google.api.method_signature)": "name"}]}}}, "ListDatabasesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "firestore.googleapis.com/Database"}}, "showDeleted": {"type": "bool", "id": 4}}}, "CreateDatabaseRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "firestore.googleapis.com/Database"}}, "database": {"type": "Database", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "databaseId": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "CreateDatabaseMetadata": {"fields": {}}, "ListDatabasesResponse": {"fields": {"databases": {"rule": "repeated", "type": "Database", "id": 1}, "unreachable": {"rule": "repeated", "type": "string", "id": 3}}}, "GetDatabaseRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}}}, "UpdateDatabaseRequest": {"fields": {"database": {"type": "Database", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "UpdateDatabaseMetadata": {"fields": {}}, "DeleteDatabaseRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "etag": {"type": "string", "id": 3}}}, "DeleteDatabaseMetadata": {"fields": {}}, "CreateBackupScheduleRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "backupSchedule": {"type": "BackupSchedule", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "GetBackupScheduleRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/BackupSchedule"}}}}, "UpdateBackupScheduleRequest": {"fields": {"backupSchedule": {"type": "BackupSchedule", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "ListBackupSchedulesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}}}, "ListBackupSchedulesResponse": {"fields": {"backupSchedules": {"rule": "repeated", "type": "BackupSchedule", "id": 1}}}, "DeleteBackupScheduleRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/BackupSchedule"}}}}, "CreateIndexRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/CollectionGroup"}}, "index": {"type": "Index", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "ListIndexesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/CollectionGroup"}}, "filter": {"type": "string", "id": 2}, "pageSize": {"type": "int32", "id": 3}, "pageToken": {"type": "string", "id": 4}}}, "ListIndexesResponse": {"fields": {"indexes": {"rule": "repeated", "type": "Index", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "GetIndexRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Index"}}}}, "DeleteIndexRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Index"}}}}, "UpdateFieldRequest": {"fields": {"field": {"type": "Field", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "GetFieldRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Field"}}}}, "ListFieldsRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/CollectionGroup"}}, "filter": {"type": "string", "id": 2}, "pageSize": {"type": "int32", "id": 3}, "pageToken": {"type": "string", "id": 4}}}, "ListFieldsResponse": {"fields": {"fields": {"rule": "repeated", "type": "Field", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "ExportDocumentsRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "collectionIds": {"rule": "repeated", "type": "string", "id": 2}, "outputUriPrefix": {"type": "string", "id": 3}, "namespaceIds": {"rule": "repeated", "type": "string", "id": 4}, "snapshotTime": {"type": "google.protobuf.Timestamp", "id": 5}}}, "ImportDocumentsRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "collectionIds": {"rule": "repeated", "type": "string", "id": 2}, "inputUriPrefix": {"type": "string", "id": 3}, "namespaceIds": {"rule": "repeated", "type": "string", "id": 4}}}, "BulkDeleteDocumentsRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "collectionIds": {"rule": "repeated", "type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "namespaceIds": {"rule": "repeated", "type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "BulkDeleteDocumentsResponse": {"fields": {}}, "GetBackupRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Backup"}}}}, "ListBackupsRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Location"}}, "filter": {"type": "string", "id": 2}}}, "ListBackupsResponse": {"fields": {"backups": {"rule": "repeated", "type": "Backup", "id": 1}, "unreachable": {"rule": "repeated", "type": "string", "id": 3}}}, "DeleteBackupRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Backup"}}}}, "RestoreDatabaseRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "firestore.googleapis.com/Database"}}, "databaseId": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "backup": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "firestore.googleapis.com/Backup"}}, "encryptionConfig": {"type": "Database.EncryptionConfig", "id": 9, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "IndexOperationMetadata": {"fields": {"startTime": {"type": "google.protobuf.Timestamp", "id": 1}, "endTime": {"type": "google.protobuf.Timestamp", "id": 2}, "index": {"type": "string", "id": 3}, "state": {"type": "OperationState", "id": 4}, "progressDocuments": {"type": "Progress", "id": 5}, "progressBytes": {"type": "Progress", "id": 6}}}, "FieldOperationMetadata": {"fields": {"startTime": {"type": "google.protobuf.Timestamp", "id": 1}, "endTime": {"type": "google.protobuf.Timestamp", "id": 2}, "field": {"type": "string", "id": 3}, "indexConfigDeltas": {"rule": "repeated", "type": "IndexConfigDelta", "id": 4}, "state": {"type": "OperationState", "id": 5}, "progressDocuments": {"type": "Progress", "id": 6}, "progressBytes": {"type": "Progress", "id": 7}, "ttlConfigDelta": {"type": "TtlConfigDelta", "id": 8}}, "nested": {"IndexConfigDelta": {"fields": {"changeType": {"type": "ChangeType", "id": 1}, "index": {"type": "Index", "id": 2}}, "nested": {"ChangeType": {"values": {"CHANGE_TYPE_UNSPECIFIED": 0, "ADD": 1, "REMOVE": 2}}}}, "TtlConfigDelta": {"fields": {"changeType": {"type": "ChangeType", "id": 1}}, "nested": {"ChangeType": {"values": {"CHANGE_TYPE_UNSPECIFIED": 0, "ADD": 1, "REMOVE": 2}}}}}}, "ExportDocumentsMetadata": {"fields": {"startTime": {"type": "google.protobuf.Timestamp", "id": 1}, "endTime": {"type": "google.protobuf.Timestamp", "id": 2}, "operationState": {"type": "OperationState", "id": 3}, "progressDocuments": {"type": "Progress", "id": 4}, "progressBytes": {"type": "Progress", "id": 5}, "collectionIds": {"rule": "repeated", "type": "string", "id": 6}, "outputUriPrefix": {"type": "string", "id": 7}, "namespaceIds": {"rule": "repeated", "type": "string", "id": 8}, "snapshotTime": {"type": "google.protobuf.Timestamp", "id": 9}}}, "ImportDocumentsMetadata": {"fields": {"startTime": {"type": "google.protobuf.Timestamp", "id": 1}, "endTime": {"type": "google.protobuf.Timestamp", "id": 2}, "operationState": {"type": "OperationState", "id": 3}, "progressDocuments": {"type": "Progress", "id": 4}, "progressBytes": {"type": "Progress", "id": 5}, "collectionIds": {"rule": "repeated", "type": "string", "id": 6}, "inputUriPrefix": {"type": "string", "id": 7}, "namespaceIds": {"rule": "repeated", "type": "string", "id": 8}}}, "BulkDeleteDocumentsMetadata": {"fields": {"startTime": {"type": "google.protobuf.Timestamp", "id": 1}, "endTime": {"type": "google.protobuf.Timestamp", "id": 2}, "operationState": {"type": "OperationState", "id": 3}, "progressDocuments": {"type": "Progress", "id": 4}, "progressBytes": {"type": "Progress", "id": 5}, "collectionIds": {"rule": "repeated", "type": "string", "id": 6}, "namespaceIds": {"rule": "repeated", "type": "string", "id": 7}, "snapshotTime": {"type": "google.protobuf.Timestamp", "id": 8}}}, "ExportDocumentsResponse": {"fields": {"outputUriPrefix": {"type": "string", "id": 1}}}, "RestoreDatabaseMetadata": {"fields": {"startTime": {"type": "google.protobuf.Timestamp", "id": 1}, "endTime": {"type": "google.protobuf.Timestamp", "id": 2}, "operationState": {"type": "OperationState", "id": 3}, "database": {"type": "string", "id": 4, "options": {"(google.api.resource_reference).type": "firestore.googleapis.com/Database"}}, "backup": {"type": "string", "id": 5, "options": {"(google.api.resource_reference).type": "firestore.googleapis.com/Backup"}}, "progressPercentage": {"type": "Progress", "id": 8}}}, "Progress": {"fields": {"estimatedWork": {"type": "int64", "id": 1}, "completedWork": {"type": "int64", "id": 2}}}, "OperationState": {"values": {"OPERATION_STATE_UNSPECIFIED": 0, "INITIALIZING": 1, "PROCESSING": 2, "CANCELLING": 3, "FINALIZING": 4, "SUCCESSFUL": 5, "FAILED": 6, "CANCELLED": 7}}, "BackupSchedule": {"options": {"(google.api.resource).type": "firestore.googleapis.com/BackupSchedule", "(google.api.resource).pattern": "projects/{project}/databases/{database}/backupSchedules/{backup_schedule}"}, "oneofs": {"recurrence": {"oneof": ["dailyRecurrence", "weeklyRecurrence"]}}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 3, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "retention": {"type": "google.protobuf.Duration", "id": 6}, "dailyRecurrence": {"type": "DailyRecurrence", "id": 7}, "weeklyRecurrence": {"type": "WeeklyRecurrence", "id": 8}}}, "DailyRecurrence": {"fields": {}}, "WeeklyRecurrence": {"fields": {"day": {"type": "google.type.DayOfWeek", "id": 2}}}, "LocationMetadata": {"fields": {}}}}}}}}, "api": {"options": {"go_package": "google.golang.org/genproto/googleapis/api;api", "java_multiple_files": true, "java_outer_classname": "LaunchStageProto", "java_package": "com.google.api", "objc_class_prefix": "GAPI", "cc_enable_arenas": true}, "nested": {"fieldBehavior": {"rule": "repeated", "type": "google.api.FieldBehavior", "id": 1052, "extend": "google.protobuf.FieldOptions", "options": {"packed": false}}, "FieldBehavior": {"values": {"FIELD_BEHAVIOR_UNSPECIFIED": 0, "OPTIONAL": 1, "REQUIRED": 2, "OUTPUT_ONLY": 3, "INPUT_ONLY": 4, "IMMUTABLE": 5, "UNORDERED_LIST": 6, "NON_EMPTY_DEFAULT": 7, "IDENTIFIER": 8}}, "resourceReference": {"type": "google.api.ResourceReference", "id": 1055, "extend": "google.protobuf.FieldOptions"}, "resourceDefinition": {"rule": "repeated", "type": "google.api.ResourceDescriptor", "id": 1053, "extend": "google.protobuf.FileOptions"}, "resource": {"type": "google.api.ResourceDescriptor", "id": 1053, "extend": "google.protobuf.MessageOptions"}, "ResourceDescriptor": {"fields": {"type": {"type": "string", "id": 1}, "pattern": {"rule": "repeated", "type": "string", "id": 2}, "nameField": {"type": "string", "id": 3}, "history": {"type": "History", "id": 4}, "plural": {"type": "string", "id": 5}, "singular": {"type": "string", "id": 6}, "style": {"rule": "repeated", "type": "Style", "id": 10}}, "nested": {"History": {"values": {"HISTORY_UNSPECIFIED": 0, "ORIGINALLY_SINGLE_PATTERN": 1, "FUTURE_MULTI_PATTERN": 2}}, "Style": {"values": {"STYLE_UNSPECIFIED": 0, "DECLARATIVE_FRIENDLY": 1}}}}, "ResourceReference": {"fields": {"type": {"type": "string", "id": 1}, "childType": {"type": "string", "id": 2}}}, "http": {"type": "HttpRule", "id": 72295728, "extend": "google.protobuf.MethodOptions"}, "Http": {"fields": {"rules": {"rule": "repeated", "type": "HttpRule", "id": 1}, "fullyDecodeReservedExpansion": {"type": "bool", "id": 2}}}, "HttpRule": {"oneofs": {"pattern": {"oneof": ["get", "put", "post", "delete", "patch", "custom"]}}, "fields": {"selector": {"type": "string", "id": 1}, "get": {"type": "string", "id": 2}, "put": {"type": "string", "id": 3}, "post": {"type": "string", "id": 4}, "delete": {"type": "string", "id": 5}, "patch": {"type": "string", "id": 6}, "custom": {"type": "CustomHttpPattern", "id": 8}, "body": {"type": "string", "id": 7}, "responseBody": {"type": "string", "id": 12}, "additionalBindings": {"rule": "repeated", "type": "HttpRule", "id": 11}}}, "CustomHttpPattern": {"fields": {"kind": {"type": "string", "id": 1}, "path": {"type": "string", "id": 2}}}, "methodSignature": {"rule": "repeated", "type": "string", "id": 1051, "extend": "google.protobuf.MethodOptions"}, "defaultHost": {"type": "string", "id": 1049, "extend": "google.protobuf.ServiceOptions"}, "oauthScopes": {"type": "string", "id": 1050, "extend": "google.protobuf.ServiceOptions"}, "apiVersion": {"type": "string", "id": 525000001, "extend": "google.protobuf.ServiceOptions"}, "CommonLanguageSettings": {"fields": {"referenceDocsUri": {"type": "string", "id": 1, "options": {"deprecated": true}}, "destinations": {"rule": "repeated", "type": "ClientLibraryDestination", "id": 2}, "selectiveGapicGeneration": {"type": "SelectiveGapicGeneration", "id": 3}}}, "ClientLibrarySettings": {"fields": {"version": {"type": "string", "id": 1}, "launchStage": {"type": "LaunchStage", "id": 2}, "restNumericEnums": {"type": "bool", "id": 3}, "javaSettings": {"type": "JavaSettings", "id": 21}, "cppSettings": {"type": "CppSettings", "id": 22}, "phpSettings": {"type": "PhpSettings", "id": 23}, "pythonSettings": {"type": "PythonSettings", "id": 24}, "nodeSettings": {"type": "NodeSettings", "id": 25}, "dotnetSettings": {"type": "DotnetSettings", "id": 26}, "rubySettings": {"type": "RubySettings", "id": 27}, "goSettings": {"type": "GoSettings", "id": 28}}}, "Publishing": {"fields": {"methodSettings": {"rule": "repeated", "type": "MethodSettings", "id": 2}, "newIssueUri": {"type": "string", "id": 101}, "documentationUri": {"type": "string", "id": 102}, "apiShortName": {"type": "string", "id": 103}, "githubLabel": {"type": "string", "id": 104}, "codeownerGithubTeams": {"rule": "repeated", "type": "string", "id": 105}, "docTagPrefix": {"type": "string", "id": 106}, "organization": {"type": "ClientLibraryOrganization", "id": 107}, "librarySettings": {"rule": "repeated", "type": "ClientLibrarySettings", "id": 109}, "protoReferenceDocumentationUri": {"type": "string", "id": 110}, "restReferenceDocumentationUri": {"type": "string", "id": 111}}}, "JavaSettings": {"fields": {"libraryPackage": {"type": "string", "id": 1}, "serviceClassNames": {"keyType": "string", "type": "string", "id": 2}, "common": {"type": "CommonLanguageSettings", "id": 3}}}, "CppSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "PhpSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "PythonSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}, "experimentalFeatures": {"type": "ExperimentalFeatures", "id": 2}}, "nested": {"ExperimentalFeatures": {"fields": {"restAsyncIoEnabled": {"type": "bool", "id": 1}, "protobufPythonicTypesEnabled": {"type": "bool", "id": 2}}}}}, "NodeSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "DotnetSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}, "renamedServices": {"keyType": "string", "type": "string", "id": 2}, "renamedResources": {"keyType": "string", "type": "string", "id": 3}, "ignoredResources": {"rule": "repeated", "type": "string", "id": 4}, "forcedNamespaceAliases": {"rule": "repeated", "type": "string", "id": 5}, "handwrittenSignatures": {"rule": "repeated", "type": "string", "id": 6}}}, "RubySettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "GoSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}, "renamedServices": {"keyType": "string", "type": "string", "id": 2}}}, "MethodSettings": {"fields": {"selector": {"type": "string", "id": 1}, "longRunning": {"type": "<PERSON><PERSON><PERSON>ning", "id": 2}, "autoPopulatedFields": {"rule": "repeated", "type": "string", "id": 3}}, "nested": {"LongRunning": {"fields": {"initialPollDelay": {"type": "google.protobuf.Duration", "id": 1}, "pollDelayMultiplier": {"type": "float", "id": 2}, "maxPollDelay": {"type": "google.protobuf.Duration", "id": 3}, "totalPollTimeout": {"type": "google.protobuf.Duration", "id": 4}}}}}, "ClientLibraryOrganization": {"values": {"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED": 0, "CLOUD": 1, "ADS": 2, "PHOTOS": 3, "STREET_VIEW": 4, "SHOPPING": 5, "GEO": 6, "GENERATIVE_AI": 7}}, "ClientLibraryDestination": {"values": {"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED": 0, "GITHUB": 10, "PACKAGE_MANAGER": 20}}, "SelectiveGapicGeneration": {"fields": {"methods": {"rule": "repeated", "type": "string", "id": 1}, "generateOmittedAsInternal": {"type": "bool", "id": 2}}}, "LaunchStage": {"values": {"LAUNCH_STAGE_UNSPECIFIED": 0, "UNIMPLEMENTED": 6, "PRELAUNCH": 7, "EARLY_ACCESS": 1, "ALPHA": 2, "BETA": 3, "GA": 4, "DEPRECATED": 5}}}}, "protobuf": {"options": {"go_package": "google.golang.org/protobuf/types/descriptorpb", "java_package": "com.google.protobuf", "java_outer_classname": "DescriptorProtos", "csharp_namespace": "Google.Protobuf.Reflection", "objc_class_prefix": "GPB", "cc_enable_arenas": true, "optimize_for": "SPEED"}, "nested": {"FileDescriptorSet": {"fields": {"file": {"rule": "repeated", "type": "FileDescriptorProto", "id": 1}}}, "Edition": {"values": {"EDITION_UNKNOWN": 0, "EDITION_PROTO2": 998, "EDITION_PROTO3": 999, "EDITION_2023": 1000.0, "EDITION_2024": 1001, "EDITION_1_TEST_ONLY": 1, "EDITION_2_TEST_ONLY": 2, "EDITION_99997_TEST_ONLY": 99997, "EDITION_99998_TEST_ONLY": 99998, "EDITION_99999_TEST_ONLY": 99999, "EDITION_MAX": 2147483647}}, "FileDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "package": {"type": "string", "id": 2}, "dependency": {"rule": "repeated", "type": "string", "id": 3}, "publicDependency": {"rule": "repeated", "type": "int32", "id": 10, "options": {"packed": false}}, "weakDependency": {"rule": "repeated", "type": "int32", "id": 11, "options": {"packed": false}}, "messageType": {"rule": "repeated", "type": "DescriptorProto", "id": 4}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 5}, "service": {"rule": "repeated", "type": "ServiceDescriptorProto", "id": 6}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 7}, "options": {"type": "FileOptions", "id": 8}, "sourceCodeInfo": {"type": "SourceCodeInfo", "id": 9}, "syntax": {"type": "string", "id": 12}, "edition": {"type": "Edition", "id": 14}}}, "DescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "field": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 2}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 6}, "nestedType": {"rule": "repeated", "type": "DescriptorProto", "id": 3}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 4}, "extensionRange": {"rule": "repeated", "type": "ExtensionRange", "id": 5}, "oneofDecl": {"rule": "repeated", "type": "OneofDescriptorProto", "id": 8}, "options": {"type": "MessageOptions", "id": 7}, "reservedRange": {"rule": "repeated", "type": "ReservedRange", "id": 9}, "reservedName": {"rule": "repeated", "type": "string", "id": 10}}, "nested": {"ExtensionRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}, "options": {"type": "ExtensionRangeOptions", "id": 3}}}, "ReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "ExtensionRangeOptions": {"fields": {"uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}, "declaration": {"rule": "repeated", "type": "Declaration", "id": 2, "options": {"retention": "RETENTION_SOURCE"}}, "features": {"type": "FeatureSet", "id": 50}, "verification": {"type": "VerificationState", "id": 3, "options": {"default": "UNVERIFIED", "retention": "RETENTION_SOURCE"}}}, "extensions": [[1000.0, 536870911]], "nested": {"Declaration": {"fields": {"number": {"type": "int32", "id": 1}, "fullName": {"type": "string", "id": 2}, "type": {"type": "string", "id": 3}, "reserved": {"type": "bool", "id": 5}, "repeated": {"type": "bool", "id": 6}}, "reserved": [[4, 4]]}, "VerificationState": {"values": {"DECLARATION": 0, "UNVERIFIED": 1}}}}, "FieldDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 3}, "label": {"type": "Label", "id": 4}, "type": {"type": "Type", "id": 5}, "typeName": {"type": "string", "id": 6}, "extendee": {"type": "string", "id": 2}, "defaultValue": {"type": "string", "id": 7}, "oneofIndex": {"type": "int32", "id": 9}, "jsonName": {"type": "string", "id": 10}, "options": {"type": "FieldOptions", "id": 8}, "proto3Optional": {"type": "bool", "id": 17}}, "nested": {"Type": {"values": {"TYPE_DOUBLE": 1, "TYPE_FLOAT": 2, "TYPE_INT64": 3, "TYPE_UINT64": 4, "TYPE_INT32": 5, "TYPE_FIXED64": 6, "TYPE_FIXED32": 7, "TYPE_BOOL": 8, "TYPE_STRING": 9, "TYPE_GROUP": 10, "TYPE_MESSAGE": 11, "TYPE_BYTES": 12, "TYPE_UINT32": 13, "TYPE_ENUM": 14, "TYPE_SFIXED32": 15, "TYPE_SFIXED64": 16, "TYPE_SINT32": 17, "TYPE_SINT64": 18}}, "Label": {"values": {"LABEL_OPTIONAL": 1, "LABEL_REPEATED": 3, "LABEL_REQUIRED": 2}}}}, "OneofDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "options": {"type": "OneofOptions", "id": 2}}}, "EnumDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "value": {"rule": "repeated", "type": "EnumValueDescriptorProto", "id": 2}, "options": {"type": "EnumOptions", "id": 3}, "reservedRange": {"rule": "repeated", "type": "EnumReservedRange", "id": 4}, "reservedName": {"rule": "repeated", "type": "string", "id": 5}}, "nested": {"EnumReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "EnumValueDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 2}, "options": {"type": "EnumValueOptions", "id": 3}}}, "ServiceDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "method": {"rule": "repeated", "type": "MethodDescriptorProto", "id": 2}, "options": {"type": "ServiceOptions", "id": 3}}}, "MethodDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "inputType": {"type": "string", "id": 2}, "outputType": {"type": "string", "id": 3}, "options": {"type": "MethodOptions", "id": 4}, "clientStreaming": {"type": "bool", "id": 5, "options": {"default": false}}, "serverStreaming": {"type": "bool", "id": 6, "options": {"default": false}}}}, "FileOptions": {"fields": {"javaPackage": {"type": "string", "id": 1}, "javaOuterClassname": {"type": "string", "id": 8}, "javaMultipleFiles": {"type": "bool", "id": 10, "options": {"default": false}}, "javaGenerateEqualsAndHash": {"type": "bool", "id": 20, "options": {"deprecated": true}}, "javaStringCheckUtf8": {"type": "bool", "id": 27, "options": {"default": false}}, "optimizeFor": {"type": "OptimizeMode", "id": 9, "options": {"default": "SPEED"}}, "goPackage": {"type": "string", "id": 11}, "ccGenericServices": {"type": "bool", "id": 16, "options": {"default": false}}, "javaGenericServices": {"type": "bool", "id": 17, "options": {"default": false}}, "pyGenericServices": {"type": "bool", "id": 18, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 23, "options": {"default": false}}, "ccEnableArenas": {"type": "bool", "id": 31, "options": {"default": true}}, "objcClassPrefix": {"type": "string", "id": 36}, "csharpNamespace": {"type": "string", "id": 37}, "swiftPrefix": {"type": "string", "id": 39}, "phpClassPrefix": {"type": "string", "id": 40}, "phpNamespace": {"type": "string", "id": 41}, "phpMetadataNamespace": {"type": "string", "id": 44}, "rubyPackage": {"type": "string", "id": 45}, "features": {"type": "FeatureSet", "id": 50}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[42, 42], [38, 38]], "nested": {"OptimizeMode": {"values": {"SPEED": 1, "CODE_SIZE": 2, "LITE_RUNTIME": 3}}}}, "MessageOptions": {"fields": {"messageSetWireFormat": {"type": "bool", "id": 1, "options": {"default": false}}, "noStandardDescriptorAccessor": {"type": "bool", "id": 2, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "mapEntry": {"type": "bool", "id": 7}, "deprecatedLegacyJsonFieldConflicts": {"type": "bool", "id": 11, "options": {"deprecated": true}}, "features": {"type": "FeatureSet", "id": 12}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[4, 4], [5, 5], [6, 6], [8, 8], [9, 9]]}, "FieldOptions": {"fields": {"ctype": {"type": "CType", "id": 1, "options": {"default": "STRING"}}, "packed": {"type": "bool", "id": 2}, "jstype": {"type": "JSType", "id": 6, "options": {"default": "JS_NORMAL"}}, "lazy": {"type": "bool", "id": 5, "options": {"default": false}}, "unverifiedLazy": {"type": "bool", "id": 15, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "weak": {"type": "bool", "id": 10, "options": {"default": false}}, "debugRedact": {"type": "bool", "id": 16, "options": {"default": false}}, "retention": {"type": "OptionRetention", "id": 17}, "targets": {"rule": "repeated", "type": "OptionTargetType", "id": 19, "options": {"packed": false}}, "editionDefaults": {"rule": "repeated", "type": "EditionDefault", "id": 20}, "features": {"type": "FeatureSet", "id": 21}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[4, 4], [18, 18]], "nested": {"CType": {"values": {"STRING": 0, "CORD": 1, "STRING_PIECE": 2}}, "JSType": {"values": {"JS_NORMAL": 0, "JS_STRING": 1, "JS_NUMBER": 2}}, "OptionRetention": {"values": {"RETENTION_UNKNOWN": 0, "RETENTION_RUNTIME": 1, "RETENTION_SOURCE": 2}}, "OptionTargetType": {"values": {"TARGET_TYPE_UNKNOWN": 0, "TARGET_TYPE_FILE": 1, "TARGET_TYPE_EXTENSION_RANGE": 2, "TARGET_TYPE_MESSAGE": 3, "TARGET_TYPE_FIELD": 4, "TARGET_TYPE_ONEOF": 5, "TARGET_TYPE_ENUM": 6, "TARGET_TYPE_ENUM_ENTRY": 7, "TARGET_TYPE_SERVICE": 8, "TARGET_TYPE_METHOD": 9}}, "EditionDefault": {"fields": {"edition": {"type": "Edition", "id": 3}, "value": {"type": "string", "id": 2}}}}}, "OneofOptions": {"fields": {"features": {"type": "FeatureSet", "id": 1}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "EnumOptions": {"fields": {"allowAlias": {"type": "bool", "id": 2}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "deprecatedLegacyJsonFieldConflicts": {"type": "bool", "id": 6, "options": {"deprecated": true}}, "features": {"type": "FeatureSet", "id": 7}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[5, 5]]}, "EnumValueOptions": {"fields": {"deprecated": {"type": "bool", "id": 1, "options": {"default": false}}, "features": {"type": "FeatureSet", "id": 2}, "debugRedact": {"type": "bool", "id": 3, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "ServiceOptions": {"fields": {"features": {"type": "FeatureSet", "id": 34}, "deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "MethodOptions": {"fields": {"deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "idempotencyLevel": {"type": "IdempotencyLevel", "id": 34, "options": {"default": "IDEMPOTENCY_UNKNOWN"}}, "features": {"type": "FeatureSet", "id": 35}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "nested": {"IdempotencyLevel": {"values": {"IDEMPOTENCY_UNKNOWN": 0, "NO_SIDE_EFFECTS": 1, "IDEMPOTENT": 2}}}}, "UninterpretedOption": {"fields": {"name": {"rule": "repeated", "type": "NamePart", "id": 2}, "identifierValue": {"type": "string", "id": 3}, "positiveIntValue": {"type": "uint64", "id": 4}, "negativeIntValue": {"type": "int64", "id": 5}, "doubleValue": {"type": "double", "id": 6}, "stringValue": {"type": "bytes", "id": 7}, "aggregateValue": {"type": "string", "id": 8}}, "nested": {"NamePart": {"fields": {"namePart": {"rule": "required", "type": "string", "id": 1}, "isExtension": {"rule": "required", "type": "bool", "id": 2}}}}}, "FeatureSet": {"fields": {"fieldPresence": {"type": "FieldPresence", "id": 1, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_2023", "edition_defaults.value": "EXPLICIT"}}, "enumType": {"type": "EnumType", "id": 2, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "OPEN"}}, "repeatedFieldEncoding": {"type": "RepeatedFieldEncoding", "id": 3, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "PACKED"}}, "utf8Validation": {"type": "Utf8Validation", "id": 4, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "VERIFY"}}, "messageEncoding": {"type": "MessageEncoding", "id": 5, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO2", "edition_defaults.value": "LENGTH_PREFIXED"}}, "jsonFormat": {"type": "JsonFormat", "id": 6, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "ALLOW"}}}, "extensions": [[1000.0, 1000.0], [1001, 1001], [1002, 1002], [9995, 9999], [10000.0, 10000.0]], "reserved": [[999, 999]], "nested": {"FieldPresence": {"values": {"FIELD_PRESENCE_UNKNOWN": 0, "EXPLICIT": 1, "IMPLICIT": 2, "LEGACY_REQUIRED": 3}}, "EnumType": {"values": {"ENUM_TYPE_UNKNOWN": 0, "OPEN": 1, "CLOSED": 2}}, "RepeatedFieldEncoding": {"values": {"REPEATED_FIELD_ENCODING_UNKNOWN": 0, "PACKED": 1, "EXPANDED": 2}}, "Utf8Validation": {"values": {"UTF8_VALIDATION_UNKNOWN": 0, "VERIFY": 2, "NONE": 3}}, "MessageEncoding": {"values": {"MESSAGE_ENCODING_UNKNOWN": 0, "LENGTH_PREFIXED": 1, "DELIMITED": 2}}, "JsonFormat": {"values": {"JSON_FORMAT_UNKNOWN": 0, "ALLOW": 1, "LEGACY_BEST_EFFORT": 2}}}}, "FeatureSetDefaults": {"fields": {"defaults": {"rule": "repeated", "type": "FeatureSetEditionDefault", "id": 1}, "minimumEdition": {"type": "Edition", "id": 4}, "maximumEdition": {"type": "Edition", "id": 5}}, "nested": {"FeatureSetEditionDefault": {"fields": {"edition": {"type": "Edition", "id": 3}, "features": {"type": "FeatureSet", "id": 2}}}}}, "SourceCodeInfo": {"fields": {"location": {"rule": "repeated", "type": "Location", "id": 1}}, "nested": {"Location": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1}, "span": {"rule": "repeated", "type": "int32", "id": 2}, "leadingComments": {"type": "string", "id": 3}, "trailingComments": {"type": "string", "id": 4}, "leadingDetachedComments": {"rule": "repeated", "type": "string", "id": 6}}}}}, "GeneratedCodeInfo": {"fields": {"annotation": {"rule": "repeated", "type": "Annotation", "id": 1}}, "nested": {"Annotation": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1}, "sourceFile": {"type": "string", "id": 2}, "begin": {"type": "int32", "id": 3}, "end": {"type": "int32", "id": 4}, "semantic": {"type": "Semantic", "id": 5}}, "nested": {"Semantic": {"values": {"NONE": 0, "SET": 1, "ALIAS": 2}}}}}}, "Timestamp": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}, "Duration": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}, "Any": {"fields": {"type_url": {"type": "string", "id": 1}, "value": {"type": "bytes", "id": 2}}}, "Empty": {"fields": {}}, "FieldMask": {"fields": {"paths": {"rule": "repeated", "type": "string", "id": 1}}}, "Struct": {"fields": {"fields": {"keyType": "string", "type": "Value", "id": 1}}}, "Value": {"oneofs": {"kind": {"oneof": ["nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue"]}}, "fields": {"nullValue": {"type": "Null<PERSON><PERSON>ue", "id": 1}, "numberValue": {"type": "double", "id": 2}, "stringValue": {"type": "string", "id": 3}, "boolValue": {"type": "bool", "id": 4}, "structValue": {"type": "Struct", "id": 5}, "listValue": {"type": "ListValue", "id": 6}}}, "NullValue": {"values": {"NULL_VALUE": 0}}, "ListValue": {"fields": {"values": {"rule": "repeated", "type": "Value", "id": 1}}}, "DoubleValue": {"fields": {"value": {"type": "double", "id": 1}}}, "FloatValue": {"fields": {"value": {"type": "float", "id": 1}}}, "Int64Value": {"fields": {"value": {"type": "int64", "id": 1}}}, "UInt64Value": {"fields": {"value": {"type": "uint64", "id": 1}}}, "Int32Value": {"fields": {"value": {"type": "int32", "id": 1}}}, "UInt32Value": {"fields": {"value": {"type": "uint32", "id": 1}}}, "BoolValue": {"fields": {"value": {"type": "bool", "id": 1}}}, "StringValue": {"fields": {"value": {"type": "string", "id": 1}}}, "BytesValue": {"fields": {"value": {"type": "bytes", "id": 1}}}}}, "type": {"options": {"go_package": "google.golang.org/genproto/googleapis/type/latlng;latlng", "java_multiple_files": true, "java_outer_classname": "LatLngProto", "java_package": "com.google.type", "objc_class_prefix": "GTP", "cc_enable_arenas": true}, "nested": {"DayOfWeek": {"values": {"DAY_OF_WEEK_UNSPECIFIED": 0, "MONDAY": 1, "TUESDAY": 2, "WEDNESDAY": 3, "THURSDAY": 4, "FRIDAY": 5, "SATURDAY": 6, "SUNDAY": 7}}, "LatLng": {"fields": {"latitude": {"type": "double", "id": 1}, "longitude": {"type": "double", "id": 2}}}}}, "longrunning": {"options": {"cc_enable_arenas": true, "csharp_namespace": "Google.LongRunning", "go_package": "cloud.google.com/go/longrunning/autogen/longrunningpb;longrunningpb", "java_multiple_files": true, "java_outer_classname": "OperationsProto", "java_package": "com.google.longrunning", "objc_class_prefix": "GLRUN", "php_namespace": "Google\\LongRunning"}, "nested": {"operationInfo": {"type": "google.longrunning.OperationInfo", "id": 1049, "extend": "google.protobuf.MethodOptions"}, "Operations": {"options": {"(google.api.default_host)": "longrunning.googleapis.com"}, "methods": {"ListOperations": {"requestType": "ListOperationsRequest", "responseType": "ListOperationsResponse", "options": {"(google.api.http).get": "/v1/{name=operations}", "(google.api.method_signature)": "name,filter"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=operations}"}}, {"(google.api.method_signature)": "name,filter"}]}, "GetOperation": {"requestType": "GetOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/v1/{name=operations/**}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=operations/**}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteOperation": {"requestType": "DeleteOperationRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=operations/**}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=operations/**}"}}, {"(google.api.method_signature)": "name"}]}, "CancelOperation": {"requestType": "CancelOperationRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).post": "/v1/{name=operations/**}:cancel", "(google.api.http).body": "*", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{name=operations/**}:cancel", "body": "*"}}, {"(google.api.method_signature)": "name"}]}, "WaitOperation": {"requestType": "WaitOperationRequest", "responseType": "Operation"}}}, "Operation": {"oneofs": {"result": {"oneof": ["error", "response"]}}, "fields": {"name": {"type": "string", "id": 1}, "metadata": {"type": "google.protobuf.Any", "id": 2}, "done": {"type": "bool", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}, "response": {"type": "google.protobuf.Any", "id": 5}}}, "GetOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "ListOperationsRequest": {"fields": {"name": {"type": "string", "id": 4}, "filter": {"type": "string", "id": 1}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListOperationsResponse": {"fields": {"operations": {"rule": "repeated", "type": "Operation", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "CancelOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "DeleteOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "WaitOperationRequest": {"fields": {"name": {"type": "string", "id": 1}, "timeout": {"type": "google.protobuf.Duration", "id": 2}}}, "OperationInfo": {"fields": {"responseType": {"type": "string", "id": 1}, "metadataType": {"type": "string", "id": 2}}}}}, "rpc": {"options": {"cc_enable_arenas": true, "go_package": "google.golang.org/genproto/googleapis/rpc/status;status", "java_multiple_files": true, "java_outer_classname": "StatusProto", "java_package": "com.google.rpc", "objc_class_prefix": "RPC"}, "nested": {"Status": {"fields": {"code": {"type": "int32", "id": 1}, "message": {"type": "string", "id": 2}, "details": {"rule": "repeated", "type": "google.protobuf.Any", "id": 3}}}}}}}}}