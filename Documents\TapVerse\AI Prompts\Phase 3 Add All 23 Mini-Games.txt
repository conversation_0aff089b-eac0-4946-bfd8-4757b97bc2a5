Phase 3: Add All 23 Mini-Games
Objectives:

One game = one feature folder

Each game gets: game widget, assets, sounds, score logic

Tasks:

1 game at a time: each folder like lib/features/games/flappy/flappy_game.dart

🧠 Prompt:

“Create a Flutter widget FlappyGame in lib/features/games/flappy/flappy_game.dart. Tapping makes the bird flap upward. Pass through pipes. Score = number of pipes passed. Show GameOverDialog. Submit score to Firestore and reward tokens.”

Repeat this for each of the 23 games:
1. SwishShot
plaintext
Copy
Edit
Create a widget called `SwishShotGame` in `lib/features/games/swish_shot/swish_shot_game.dart`.

Game: SwishShot  
Interaction Type: Flick

Gameplay:
- Flick a basketball toward a moving hoop.
- Score if ball enters hoop. Reset ball after shot.
- End game after 3 missed shots.

Visuals:
- Animate ball motion with flutter_animate.
- Use Lottie animation and sound (`swish.mp3`) on score.
- Trigger vibration on successful shot.

Game Over:
- Show GameOverDialog (score, high score, tokens = score × 2).
- Submit score to `leaderboards/swish_shot`
- Update `users/{uid}/tokens`
2. TappyFooty
plaintext
Copy
Edit
Create a widget called `TappyFootyGame` in `lib/features/games/tappy_footy/tappy_footy_game.dart`.

Game: TappyFooty  
Interaction Type: Tap

Gameplay:
- Tap the ball to bounce it upward.
- Each tap adds 1 to score.
- Ball falls to bottom = game over.

Visuals:
- Animate bounce and ball motion.
- Play `kick.mp3` and vibrate on tap.

Game Over:
- Show GameOverDialog with tokens = score × 1
- Submit score to `leaderboards/tappy_footy`
- Update `users/{uid}/tokens`
3. Rebounder
plaintext
Copy
Edit
Create a widget called `RebounderGame` in `lib/features/games/rebounder/rebounder_game.dart`.

Game: Rebounder  
Interaction Type: Drag

Gameplay:
- Ball bounces around screen.
- Drag paddle to reflect ball.
- Ball gets faster with each hit. Miss = game over.

Visuals:
- Animate paddle/ball with flutter_animate.
- Play `bounce.mp3`, vibrate on hit.

Game Over:
- GameOverDialog, tokens = score × 2
- Submit score to `leaderboards/rebounder`
- Update `users/{uid}/tokens`
4. SpikeLoop
plaintext
Copy
Edit
Create a widget called `SpikeLoopGame` in `lib/features/games/spike_loop/spike_loop_game.dart`.

Game: SpikeLoop  
Interaction Type: Tap

Gameplay:
- Tap to keep volleyball bouncing.
- Score increases per bounce.
- Ball touches ground = game over.

Visuals:
- Ball bounce animation with flutter_animate.
- Play `boing.mp3`, vibrate on successful bounce.

Game Over:
- Show GameOverDialog, tokens = score × 1.
- Submit to `leaderboards/spike_loop`
- Update `users/{uid}/tokens`
5. Flapster
plaintext
Copy
Edit
Create a widget called `FlapsterGame` in `lib/features/games/flapster/flapster_game.dart`.

Game: Flapster  
Interaction Type: Tap

Gameplay:
- Tap to flap and fly through gaps.
- Score increases per pipe passed.

Visuals:
- Bird flaps with flutter_animate.
- Play `flap.mp3`, vibrate on tap.

Game Over:
- GameOverDialog, tokens = score × 2
- Submit score to `leaderboards/flapster`
- Update `users/{uid}/tokens`
6. SmashWall
plaintext
Copy
Edit
Create a widget called `SmashWallGame` in `lib/features/games/smash_wall/smash_wall_game.dart`.

Game: SmashWall  
Interaction Type: Drag

Gameplay:
- Drag paddle to reflect ball.
- Break bricks to score.

Visuals:
- Ball physics and brick explosions.
- Play `crash.mp3`, vibrate on brick hit.

Game Over:
- GameOverDialog, tokens = score × 2
- Submit score to `leaderboards/smash_wall`
- Update `users/{uid}/tokens`
7. AstroZap
plaintext
Copy
Edit
Create a widget called `AstroZapGame` in `lib/features/games/astro_zap/astro_zap_game.dart`.

Game: AstroZap  
Interaction Type: Drag + Tap

Gameplay:
- Drag to move spaceship, tap to shoot.
- Destroy asteroids = score.

Visuals:
- Starfield and explosion animations.
- Play `laser.mp3`, `explode.mp3`, vibrate on hit.

Game Over:
- GameOverDialog, tokens = score × 3
- Submit score to `leaderboards/astro_zap`
- Update `users/{uid}/tokens`
8. Wriggle
plaintext
Copy
Edit
Create a widget called `WriggleGame` in `lib/features/games/wriggle/wriggle_game.dart`.

Game: Wriggle  
Interaction Type: Swipe

Gameplay:
- Swipe to change snake direction.
- Eat items to grow. Don’t hit self/wall.

Visuals:
- Smooth snake animation, glowing food.
- Play `chomp.mp3`, vibrate on eat.

Game Over:
- GameOverDialog, tokens = score × 1
- Submit score to `leaderboards/wriggle`
- Update `users/{uid}/tokens`
9. HueDrop
plaintext
Copy
Edit
Create a widget called `HueDropGame` in `lib/features/games/hue_drop/hue_drop_game.dart`.

Game: HueDrop  
Interaction Type: Tap

Gameplay:
- Tap to rotate color ring.
- Match falling balls to color.
- Mismatch = game over.

Visuals:
- Color ring animation.
- Play `click.mp3`, vibrate on match.

Game Over:
- GameOverDialog, tokens = score × 2
- Submit score to `leaderboards/hue_drop`
- Update `users/{uid}/tokens`
10. DashRush
plaintext
Copy
Edit
Create a widget called `DashRushGame` in `lib/features/games/dash_rush/dash_rush_game.dart`.

Game: DashRush  
Interaction Type: Swipe

Gameplay:
- Swipe to dodge obstacles.
- Distance = score.

Visuals:
- Speed lines and road scroll.
- Play `woosh.mp3`, vibrate on near miss.

Game Over:
- GameOverDialog, tokens = distance / 10
- Submit score to `leaderboards/dash_rush`
- Update `users/{uid}/tokens`
🔄 [Prompt continues for remaining games 11–23]
Would you like me to continue with the remaining 13 games in this format?

Let me know, and I’ll complete the rest for you.








You said:
yes


ChatGPT said:
Here’s the continuation of the prompt using your unique game names for games 11–23, following the same format:

11. BeatTap
plaintext
Copy
Edit
Create a widget called `BeatTapGame` in `lib/features/games/beat_tap/beat_tap_game.dart`.

Game: BeatTap  
Interaction Type: Tap

Gameplay:
- Tap only the black tiles in rhythm.
- Speed increases over time.
- Miss a beat = game over.

Visuals:
- Tiles move rhythmically with animation.
- Play `note.mp3`, vibrate on successful tap.

Game Over:
- GameOverDialog, tokens = score × 1
- Submit score to `leaderboards/beat_tap`
- Update `users/{uid}/tokens`
12. BoomSweep
plaintext
Copy
Edit
Create a widget called `BoomSweepGame` in `lib/features/games/boom_sweep/boom_sweep_game.dart`.

Game: BoomSweep  
Interaction Type: Tap

Gameplay:
- Classic minesweeper logic.
- Tap to reveal tiles, flag suspected mines.
- Hit a mine = game over.

Visuals:
- Grid reveal animations.
- Play `click.mp3`, vibrate on mine reveal.

Game Over:
- GameOverDialog, tokens = cleared tiles × 1
- Submit score to `leaderboards/boom_sweep`
- Update `users/{uid}/tokens`
13. FruitSnag
plaintext
Copy
Edit
Create a widget called `FruitSnagGame` in `lib/features/games/fruit_snag/fruit_snag_game.dart`.

Game: FruitSnag  
Interaction Type: Drag

Gameplay:
- Drag basket to catch falling fruits or coins.
- Missed items don’t count. Catch = +1.

Visuals:
- Fruit bounce and glow effects.
- Play `catch.mp3`, vibrate on catch.

Game Over:
- GameOverDialog, tokens = score × 1
- Submit score to `leaderboards/fruit_snag`
- Update `users/{uid}/tokens`
14. TileTwist
plaintext
Copy
Edit
Create a widget called `TileTwistGame` in `lib/features/games/tile_twist/tile_twist_game.dart`.

Game: TileTwist  
Interaction Type: Swipe

Gameplay:
- Swipe to merge tiles of the same value.
- Goal is to reach the highest tile.

Visuals:
- Sliding tile animation and glow on merge.
- Play `merge.mp3`, vibrate on successful move.

Game Over:
- GameOverDialog, tokens = highest tile value / 2
- Submit score to `leaderboards/tile_twist`
- Update `users/{uid}/tokens`
15. DartDash
plaintext
Copy
Edit
Create a widget called `DartDashGame` in `lib/features/games/dart_dash/dart_dash_game.dart`.

Game: DartDash  
Interaction Type: Flick

Gameplay:
- Flick to throw darts at target.
- Score higher for closer-to-center hits.

Visuals:
- Dart flight animation.
- Play `dart_hit.mp3`, vibrate on center hit.

Game Over:
- GameOverDialog, tokens = score × 2
- Submit score to `leaderboards/dart_dash`
- Update `users/{uid}/tokens`
16. DropTarget
plaintext
Copy
Edit
Create a widget called `DropTargetGame` in `lib/features/games/drop_target/drop_target_game.dart`.

Game: DropTarget  
Interaction Type: Tap

Gameplay:
- Tap to drop item from the top.
- Try to land into a moving target/bucket.

Visuals:
- Item fall animation and bounce.
- Play `drop.mp3`, vibrate on target land.

Game Over:
- GameOverDialog, tokens = hits × 2
- Submit score to `leaderboards/drop_target`
- Update `users/{uid}/tokens`
17. AlienSweep
plaintext
Copy
Edit
Create a widget called `AlienSweepGame` in `lib/features/games/alien_sweep/alien_sweep_game.dart`.

Game: AlienSweep  
Interaction Type: Drag + Tap

Gameplay:
- Drag to move ship, tap to shoot aliens.
- Clear waves to increase score.

Visuals:
- Alien explosion and starfield background.
- Play `zap.mp3`, `explode.mp3`, vibrate on hit.

Game Over:
- GameOverDialog, tokens = aliens killed × 2
- Submit score to `leaderboards/alien_sweep`
- Update `users/{uid}/tokens`
18. TrioMatch
plaintext
Copy
Edit
Create a widget called `TrioMatchGame` in `lib/features/games/trio_match/trio_match_game.dart`.

Game: TrioMatch  
Interaction Type: Drag

Gameplay:
- Drag tiles to align 3+ of same kind.
- Score with each successful combo.

Visuals:
- Exploding tiles and chain animation.
- Play `pop.mp3`, vibrate on match.

Game Over:
- GameOverDialog, tokens = combos × 2
- Submit score to `leaderboards/trio_match`
- Update `users/{uid}/tokens`
19. SkyHop
plaintext
Copy
Edit
Create a widget called `SkyHopGame` in `lib/features/games/sky_hop/sky_hop_game.dart`.

Game: SkyHop  
Interaction Type: Tap

Gameplay:
- Tap to jump between floating platforms.
- Score increases with each successful jump.

Visuals:
- Jump arc animation and clouds.
- Play `hop.mp3`, vibrate on land.

Game Over:
- GameOverDialog, tokens = score × 1
- Submit score to `leaderboards/sky_hop`
- Update `users/{uid}/tokens`
20. NanoGolf
plaintext
Copy
Edit
Create a widget called `NanoGolfGame` in `lib/features/games/nano_golf/nano_golf_game.dart`.

Game: NanoGolf  
Interaction Type: Flick

Gameplay:
- Flick to shoot ball toward hole.
- Fewer strokes = better score.

Visuals:
- Golf ball roll and putting animation.
- Play `golf_hit.mp3`, vibrate on hole-in-one.

Game Over:
- GameOverDialog, tokens = 10 - strokes (min 1)
- Submit score to `leaderboards/nano_golf`
- Update `users/{uid}/tokens`
21. PopBurst
plaintext
Copy
Edit
Create a widget called `PopBurstGame` in `lib/features/games/pop_burst/pop_burst_game.dart`.

Game: PopBurst  
Interaction Type: Tap

Gameplay:
- Tap balloons before they float off screen.
- Score increases per burst.

Visuals:
- Balloon burst animation with confetti.
- Play `pop.mp3`, vibrate on tap.

Game Over:
- GameOverDialog, tokens = score × 1
- Submit score to `leaderboards/pop_burst`
- Update `users/{uid}/tokens`
22. Bullseye
plaintext
Copy
Edit
Create a widget called `BullseyeGame` in `lib/features/games/bullseye/bullseye_game.dart`.

Game: Bullseye  
Interaction Type: Drag + Release

Gameplay:
- Drag and release to shoot arrows at target.
- Closer to center = higher score.

Visuals:
- Arrow flight and target impact.
- Play `twang.mp3`, vibrate on bullseye.

Game Over:
- GameOverDialog, tokens = score × 2
- Submit score to `leaderboards/bullseye`
- Update `users/{uid}/tokens`
23. Stackify
plaintext
Copy
Edit
Create a widget called `StackifyGame` in `lib/features/games/stackify/stackify_game.dart`.

Game: Stackify  
Interaction Type: Tap

Gameplay:
- Tap to stack moving blocks perfectly.
- Score increases per successful stack.
- Misaligned stack shrinks platform.

Visuals:
- Block drop animation and size reduction.
- Play `stack.mp3`, vibrate on stack.

Game Over:
- GameOverDialog, tokens = stacks × 2
- Submit score to `leaderboards/stackify`
- Update `users/{uid}/tokens`