const { onCall, HttpsError } = require('firebase-functions/v2/https');
const { getFirestore, FieldValue } = require('firebase-admin/firestore');

const db = getFirestore();

const ENTRY_COST = 40;

// Dart zone probabilities and rewards
const DART_ZONES = {
  bullseye: { reward: 300, probability: 0.02 },
  inner: { reward: 150, probability: 0.08 },
  middle: { reward: 100, probability: 0.10 },
  outer: { reward: 50, probability: 0.25 },
  miss: { reward: 0, probability: 0.55 },
};

function selectDartZone() {
  const random = Math.random();
  let cumulativeProbability = 0;

  for (const [zone, data] of Object.entries(DART_ZONES)) {
    cumulativeProbability += data.probability;
    if (random <= cumulativeProbability) {
      return { zone, ...data };
    }
  }

  // Fallback to miss (should never happen)
  return { zone: 'miss', ...DART_ZONES.miss };
}

function calculateAccuracy(zone) {
  // Calculate accuracy based on zone hit
  switch (zone) {
    case 'bullseye':
      return 1.0;
    case 'inner':
      return 0.8 + Math.random() * 0.2;
    case 'middle':
      return 0.6 + Math.random() * 0.2;
    case 'outer':
      return 0.4 + Math.random() * 0.2;
    case 'miss':
      return Math.random() * 0.4;
    default:
      return 0.0;
  }
}

exports.playLuckyDarts = onCall(async (request) => {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const uid = request.auth.uid;
    const userRef = db.collection('users').doc(uid);

    // Run transaction to ensure atomic operations
    const result = await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new HttpsError('not-found', 'User document not found');
      }

      const userData = userDoc.data();
      const currentTokens = userData.tokens || 0;

      // Check if user has enough tokens
      if (currentTokens < ENTRY_COST) {
        throw new HttpsError('failed-precondition', 'Insufficient tokens');
      }

      // Simulate dart throw
      const dartResult = selectDartZone();
      const accuracy = calculateAccuracy(dartResult.zone);
      const tokensWon = dartResult.reward;
      const newTokenBalance = currentTokens - ENTRY_COST + tokensWon;

      // Update user tokens
      transaction.update(userRef, {
        tokens: newTokenBalance,
        lastActivity: FieldValue.serverTimestamp(),
      });

      // Log casino transaction
      const transactionRef = userRef.collection('casinoTransactions').doc();
      transaction.set(transactionRef, {
        gameId: 'lucky_darts',
        type: 'throw',
        entryCost: ENTRY_COST,
        tokensWon: tokensWon,
        netGain: tokensWon - ENTRY_COST,
        gameData: {
          zone: dartResult.zone,
          accuracy: accuracy,
          probability: dartResult.probability,
        },
        timestamp: FieldValue.serverTimestamp(),
      });

      // Update casino stats
      const statsRef = userRef.collection('casinoStats').doc('lucky_darts');
      const statsDoc = await transaction.get(statsRef);
      
      if (statsDoc.exists) {
        const stats = statsDoc.data();
        transaction.update(statsRef, {
          gamesPlayed: (stats.gamesPlayed || 0) + 1,
          wins: (stats.wins || 0) + (tokensWon > 0 ? 1 : 0),
          losses: (stats.losses || 0) + (tokensWon === 0 ? 1 : 0),
          totalTokensWon: (stats.totalTokensWon || 0) + tokensWon,
          totalTokensSpent: (stats.totalTokensSpent || 0) + ENTRY_COST,
          lastPlayed: FieldValue.serverTimestamp(),
          // Track zone statistics
          bullseyeHits: (stats.bullseyeHits || 0) + (dartResult.zone === 'bullseye' ? 1 : 0),
          innerHits: (stats.innerHits || 0) + (dartResult.zone === 'inner' ? 1 : 0),
          middleHits: (stats.middleHits || 0) + (dartResult.zone === 'middle' ? 1 : 0),
          outerHits: (stats.outerHits || 0) + (dartResult.zone === 'outer' ? 1 : 0),
          misses: (stats.misses || 0) + (dartResult.zone === 'miss' ? 1 : 0),
        });
      } else {
        transaction.set(statsRef, {
          gameId: 'lucky_darts',
          gamesPlayed: 1,
          wins: tokensWon > 0 ? 1 : 0,
          losses: tokensWon === 0 ? 1 : 0,
          totalTokensWon: tokensWon,
          totalTokensSpent: ENTRY_COST,
          firstPlayed: FieldValue.serverTimestamp(),
          lastPlayed: FieldValue.serverTimestamp(),
          // Initialize zone statistics
          bullseyeHits: dartResult.zone === 'bullseye' ? 1 : 0,
          innerHits: dartResult.zone === 'inner' ? 1 : 0,
          middleHits: dartResult.zone === 'middle' ? 1 : 0,
          outerHits: dartResult.zone === 'outer' ? 1 : 0,
          misses: dartResult.zone === 'miss' ? 1 : 0,
        });
      }

      return {
        success: true,
        zone: dartResult.zone,
        accuracy: accuracy,
        tokensWon: tokensWon,
        newTokenBalance: newTokenBalance,
        entryCost: ENTRY_COST,
        zoneReward: dartResult.reward,
        zoneProbability: dartResult.probability,
      };
    });

    return result;

  } catch (error) {
    console.error('Error in playLuckyDarts function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});

// Get dart zone information
exports.getDartZoneInfo = onCall(async (request) => {
  try {
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    return {
      success: true,
      zones: DART_ZONES,
      entryCost: ENTRY_COST,
    };

  } catch (error) {
    console.error('Error in getDartZoneInfo function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});
