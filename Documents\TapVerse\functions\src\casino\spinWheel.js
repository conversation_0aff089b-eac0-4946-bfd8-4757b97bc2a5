const { onCall, HttpsError } = require('firebase-functions/v2/https');
const { getFirestore, FieldValue } = require('firebase-admin/firestore');
const { getAuth } = require('firebase-admin/auth');

const db = getFirestore();

// Spin Wheel segments with exact probabilities
const WHEEL_SEGMENTS = [
  { id: 1, label: '25', reward: 25, probability: 0.20 },
  { id: 2, label: '50', reward: 50, probability: 0.20 },
  { id: 3, label: '75', reward: 75, probability: 0.15 },
  { id: 4, label: '100', reward: 100, probability: 0.10 },
  { id: 5, label: '150', reward: 150, probability: 0.10 },
  { id: 6, label: '200', reward: 200, probability: 0.05 },
  { id: 7, label: '0', reward: 0, probability: 0.15 },
  { id: 8, label: 'LOSE ALL', reward: -1, probability: 0.05 },
];

const ENTRY_COST = 50;

function selectWinningSegment() {
  const random = Math.random();
  let cumulativeProbability = 0;

  for (const segment of WHEEL_SEGMENTS) {
    cumulativeProbability += segment.probability;
    if (random <= cumulativeProbability) {
      return segment;
    }
  }

  // Fallback to first segment (should never happen)
  return WHEEL_SEGMENTS[0];
}

exports.spinWheel = onCall(async (request) => {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const uid = request.auth.uid;
    const userRef = db.collection('users').doc(uid);

    // Run transaction to ensure atomic operations
    const result = await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new HttpsError('not-found', 'User document not found');
      }

      const userData = userDoc.data();
      const currentTokens = userData.tokens || 0;

      // Check if user has enough tokens
      if (currentTokens < ENTRY_COST) {
        throw new HttpsError('failed-precondition', 'Insufficient tokens');
      }

      // Select winning segment
      const winningSegment = selectWinningSegment();
      let tokensWon = 0;
      let newTokenBalance = currentTokens - ENTRY_COST;

      if (winningSegment.reward === -1) {
        // Lose all tokens
        tokensWon = 0;
        newTokenBalance = 0;
      } else {
        tokensWon = winningSegment.reward;
        newTokenBalance += tokensWon;
      }

      // Update user tokens
      transaction.update(userRef, {
        tokens: newTokenBalance,
        lastActivity: FieldValue.serverTimestamp(),
      });

      // Log casino transaction
      const transactionRef = userRef.collection('casinoTransactions').doc();
      transaction.set(transactionRef, {
        gameId: 'spin_wheel',
        type: 'spin',
        entryCost: ENTRY_COST,
        tokensWon: tokensWon,
        netGain: tokensWon - ENTRY_COST,
        segmentId: winningSegment.id,
        segmentLabel: winningSegment.label,
        timestamp: FieldValue.serverTimestamp(),
      });

      // Update casino stats
      const statsRef = userRef.collection('casinoStats').doc('spin_wheel');
      const statsDoc = await transaction.get(statsRef);
      
      if (statsDoc.exists) {
        const stats = statsDoc.data();
        transaction.update(statsRef, {
          gamesPlayed: (stats.gamesPlayed || 0) + 1,
          wins: (stats.wins || 0) + (tokensWon > 0 ? 1 : 0),
          losses: (stats.losses || 0) + (tokensWon === 0 ? 1 : 0),
          totalTokensWon: (stats.totalTokensWon || 0) + tokensWon,
          totalTokensSpent: (stats.totalTokensSpent || 0) + ENTRY_COST,
          lastPlayed: FieldValue.serverTimestamp(),
        });
      } else {
        transaction.set(statsRef, {
          gameId: 'spin_wheel',
          gamesPlayed: 1,
          wins: tokensWon > 0 ? 1 : 0,
          losses: tokensWon === 0 ? 1 : 0,
          totalTokensWon: tokensWon,
          totalTokensSpent: ENTRY_COST,
          firstPlayed: FieldValue.serverTimestamp(),
          lastPlayed: FieldValue.serverTimestamp(),
        });
      }

      return {
        success: true,
        segment: winningSegment,
        tokensWon: tokensWon,
        newTokenBalance: newTokenBalance,
        entryCost: ENTRY_COST,
      };
    });

    return result;

  } catch (error) {
    console.error('Error in spinWheel function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});

// Validate spin wheel result (for additional security)
exports.validateSpinResult = onCall(async (request) => {
  try {
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { segmentId, tokensWon } = request.data;
    
    if (!segmentId || tokensWon === undefined) {
      throw new HttpsError('invalid-argument', 'Missing required parameters');
    }

    // Find the segment
    const segment = WHEEL_SEGMENTS.find(s => s.id === segmentId);
    if (!segment) {
      throw new HttpsError('invalid-argument', 'Invalid segment ID');
    }

    // Validate the tokens won matches the segment
    const expectedTokens = segment.reward === -1 ? 0 : segment.reward;
    const isValid = tokensWon === expectedTokens;

    return {
      isValid: isValid,
      expectedTokens: expectedTokens,
      actualTokens: tokensWon,
      segment: segment,
    };

  } catch (error) {
    console.error('Error in validateSpinResult function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});
