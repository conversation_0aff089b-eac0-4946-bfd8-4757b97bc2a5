const { onCall, HttpsError } = require('firebase-functions/v2/https');
const { getFirestore, FieldValue } = require('firebase-admin/firestore');

const db = getFirestore();

const ENTRY_COST = 25;
const WIN_AMOUNT = 50; // 2x payout

function generateRandomCard() {
  const value = Math.floor(Math.random() * 9) + 2; // 2-10
  const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
  const suit = suits[Math.floor(Math.random() * suits.length)];
  
  return { value, suit };
}

function isGuessCorrect(firstCard, secondCard, guess) {
  if (guess === 'higher') {
    return secondCard.value > firstCard.value;
  } else if (guess === 'lower') {
    return secondCard.value < firstCard.value;
  }
  return false; // Invalid guess
}

exports.playHighLow = onCall(async (request) => {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { guess } = request.data;
    
    if (!guess || !['higher', 'lower'].includes(guess)) {
      throw new HttpsError('invalid-argument', 'Invalid guess. Must be "higher" or "lower"');
    }

    const uid = request.auth.uid;
    const userRef = db.collection('users').doc(uid);

    // Run transaction to ensure atomic operations
    const result = await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new HttpsError('not-found', 'User document not found');
      }

      const userData = userDoc.data();
      const currentTokens = userData.tokens || 0;

      // Check if user has enough tokens
      if (currentTokens < ENTRY_COST) {
        throw new HttpsError('failed-precondition', 'Insufficient tokens');
      }

      // Generate cards
      const firstCard = generateRandomCard();
      const secondCard = generateRandomCard();
      
      // Determine if guess was correct
      const isCorrect = isGuessCorrect(firstCard, secondCard, guess);
      const tokensWon = isCorrect ? WIN_AMOUNT : 0;
      const newTokenBalance = currentTokens - ENTRY_COST + tokensWon;

      // Update user tokens
      transaction.update(userRef, {
        tokens: newTokenBalance,
        lastActivity: FieldValue.serverTimestamp(),
      });

      // Log casino transaction
      const transactionRef = userRef.collection('casinoTransactions').doc();
      transaction.set(transactionRef, {
        gameId: 'high_low',
        type: 'play',
        entryCost: ENTRY_COST,
        tokensWon: tokensWon,
        netGain: tokensWon - ENTRY_COST,
        gameData: {
          firstCard: firstCard,
          secondCard: secondCard,
          guess: guess,
          isCorrect: isCorrect,
        },
        timestamp: FieldValue.serverTimestamp(),
      });

      // Update casino stats
      const statsRef = userRef.collection('casinoStats').doc('high_low');
      const statsDoc = await transaction.get(statsRef);
      
      if (statsDoc.exists) {
        const stats = statsDoc.data();
        transaction.update(statsRef, {
          gamesPlayed: (stats.gamesPlayed || 0) + 1,
          wins: (stats.wins || 0) + (isCorrect ? 1 : 0),
          losses: (stats.losses || 0) + (isCorrect ? 0 : 1),
          totalTokensWon: (stats.totalTokensWon || 0) + tokensWon,
          totalTokensSpent: (stats.totalTokensSpent || 0) + ENTRY_COST,
          lastPlayed: FieldValue.serverTimestamp(),
        });
      } else {
        transaction.set(statsRef, {
          gameId: 'high_low',
          gamesPlayed: 1,
          wins: isCorrect ? 1 : 0,
          losses: isCorrect ? 0 : 1,
          totalTokensWon: tokensWon,
          totalTokensSpent: ENTRY_COST,
          firstPlayed: FieldValue.serverTimestamp(),
          lastPlayed: FieldValue.serverTimestamp(),
        });
      }

      return {
        success: true,
        firstCard: firstCard,
        secondCard: secondCard,
        guess: guess,
        isCorrect: isCorrect,
        tokensWon: tokensWon,
        newTokenBalance: newTokenBalance,
        entryCost: ENTRY_COST,
      };
    });

    return result;

  } catch (error) {
    console.error('Error in playHighLow function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});

// Start a new High/Low game (just generates first card)
exports.startHighLow = onCall(async (request) => {
  try {
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const uid = request.auth.uid;
    const userRef = db.collection('users').doc(uid);

    // Check if user has enough tokens (but don't deduct yet)
    const userDoc = await userRef.get();
    if (!userDoc.exists) {
      throw new HttpsError('not-found', 'User document not found');
    }

    const userData = userDoc.data();
    const currentTokens = userData.tokens || 0;

    if (currentTokens < ENTRY_COST) {
      throw new HttpsError('failed-precondition', 'Insufficient tokens');
    }

    // Generate first card
    const firstCard = generateRandomCard();

    return {
      success: true,
      firstCard: firstCard,
      entryCost: ENTRY_COST,
      winAmount: WIN_AMOUNT,
    };

  } catch (error) {
    console.error('Error in startHighLow function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});
