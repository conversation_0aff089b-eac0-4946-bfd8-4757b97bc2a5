secure Firestore ruleset
here's a secure Firestore ruleset tailored specifically for <PERSON><PERSON><PERSON><PERSON>'s in-game store, including checks for:

✅ Read-only access to store items

✅ Secure token deduction on purchase

✅ Preventing duplicate ownership

✅ Restricting boost activation rules

✅ Leaderboard and score updates

🔐 TapVerse Firestore Rules (Store & User Purchases)
js
Copy
Edit
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // PUBLIC STORE ITEMS (READ-ONLY)
    match /store/items/{itemId} {
      allow read: if true;          // Anyone can view item list
      allow write: if false;        // Prevent editing from client
    }

    // BOOST STORE ITEMS (READ-ONLY)
    match /store/boosts/{boostId} {
      allow read: if true;
      allow write: if false;
    }

    // USER DATA
    match /users/{userId} {
      allow read, write: if request.auth.uid == userId;

      // Token balance
      match /tokens {
        allow read: if request.auth.uid == userId;
        allow update: if false; // Updated via Cloud Function or server only
      }

      // Owned Items
      match /ownedItems/{itemId} {
        allow read: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId
                      && !exists(/databases/$(database)/documents/users/$(userId)/ownedItems/$(itemId));
        allow delete: if false;
      }

      // Active Skins & Flair
      match /activeSkins {
        allow read, update: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId;
      }

      match /activeFlair {
        allow read, update: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId;
      }

      // Boosts (allow 1 active at a time)
      match /boosts/activeBoost {
        allow read: if request.auth.uid == userId;
        allow create: if request.auth.uid == userId
                      && !exists(/databases/$(database)/documents/users/$(userId)/boosts/activeBoost);
        allow update: if request.auth.uid == userId;
        allow delete: if request.auth.uid == userId;
      }
    }

    // LEADERBOARDS
    match /leaderboards/{gameId} {
      allow read: if true;
      allow write: if request.auth.uid != null
                   && request.resource.data.keys().hasOnly(['uid', 'score', 'timestamp'])
                   && request.resource.data.score is int;
    }
  }
}
🧠 Notes
🔐 Writes like token deduction should be handled via Cloud Functions to prevent manipulation.

🎁 Mystery Draw logic and prize assignment must also be securely handled server-side.

❌ Client is not allowed to update tokens directly (allow update: if false) — it protects your economy.

🕓 You may consider adding a purchase_history/ collection per user if you want logs of transactions.

