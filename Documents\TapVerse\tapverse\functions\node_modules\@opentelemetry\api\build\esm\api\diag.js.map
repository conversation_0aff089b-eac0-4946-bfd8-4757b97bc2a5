{"version": 3, "file": "diag.js", "sourceRoot": "", "sources": ["../../../src/api/diag.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AAC3E,OAAO,EAKL,YAAY,GACb,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAElC,IAAM,QAAQ,GAAG,MAAM,CAAC;AAExB;;;GAGG;AACH;IAYE;;;OAGG;IACH;QACE,SAAS,SAAS,CAAC,QAA0B;YAC3C,OAAO;gBAAU,cAAO;qBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;oBAAP,yBAAO;;gBACtB,IAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;gBACjC,6BAA6B;gBAC7B,IAAI,CAAC,MAAM;oBAAE,OAAO;gBACpB,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAhB,MAAM,2BAAc,IAAI,WAAE;YACnC,CAAC,CAAC;QACJ,CAAC;QAED,mFAAmF;QACnF,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,6BAA6B;QAE7B,IAAM,SAAS,GAA+B,UAC5C,MAAM,EACN,iBAAmD;;YAAnD,kCAAA,EAAA,sBAAsB,QAAQ,EAAE,YAAY,CAAC,IAAI,EAAE;YAEnD,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,mCAAmC;gBACnC,2DAA2D;gBAC3D,qFAAqF;gBACrF,IAAM,GAAG,GAAG,IAAI,KAAK,CACnB,oIAAoI,CACrI,CAAC;gBACF,IAAI,CAAC,KAAK,CAAC,MAAA,GAAG,CAAC,KAAK,mCAAI,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;gBACzC,iBAAiB,GAAG;oBAClB,QAAQ,EAAE,iBAAiB;iBAC5B,CAAC;aACH;YAED,IAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;YACpC,IAAM,SAAS,GAAG,wBAAwB,CACxC,MAAA,iBAAiB,CAAC,QAAQ,mCAAI,YAAY,CAAC,IAAI,EAC/C,MAAM,CACP,CAAC;YACF,kFAAkF;YAClF,IAAI,SAAS,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,EAAE;gBAC3D,IAAM,KAAK,GAAG,MAAA,IAAI,KAAK,EAAE,CAAC,KAAK,mCAAI,iCAAiC,CAAC;gBACrE,SAAS,CAAC,IAAI,CAAC,6CAA2C,KAAO,CAAC,CAAC;gBACnE,SAAS,CAAC,IAAI,CACZ,+DAA6D,KAAO,CACrE,CAAC;aACH;YAED,OAAO,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;QAEF,IAAI,CAAC,qBAAqB,GAAG,UAAC,OAA+B;YAC3D,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAjFD,oDAAoD;IACtC,gBAAQ,GAAtB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IA+FH,cAAC;AAAD,CAAC,AAzGD,IAyGC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagComponentLogger } from '../diag/ComponentLogger';\nimport { createLogLevelDiagLogger } from '../diag/internal/logLevelLogger';\nimport {\n  ComponentLoggerOptions,\n  DiagLogFunction,\n  DiagLogger,\n  DiagLoggerApi,\n  DiagLogLevel,\n} from '../diag/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\n\nconst API_NAME = 'diag';\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry internal\n * diagnostic API\n */\nexport class DiagAPI implements DiagLogger, DiagLoggerApi {\n  private static _instance?: DiagAPI;\n\n  /** Get the singleton instance of the DiagAPI API */\n  public static instance(): DiagAPI {\n    if (!this._instance) {\n      this._instance = new DiagAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Private internal constructor\n   * @private\n   */\n  private constructor() {\n    function _logProxy(funcName: keyof DiagLogger): DiagLogFunction {\n      return function (...args) {\n        const logger = getGlobal('diag');\n        // shortcut if logger not set\n        if (!logger) return;\n        return logger[funcName](...args);\n      };\n    }\n\n    // Using self local variable for minification purposes as 'this' cannot be minified\n    const self = this;\n\n    // DiagAPI specific functions\n\n    const setLogger: DiagLoggerApi['setLogger'] = (\n      logger,\n      optionsOrLogLevel = { logLevel: DiagLogLevel.INFO }\n    ) => {\n      if (logger === self) {\n        // There isn't much we can do here.\n        // Logging to the console might break the user application.\n        // Try to log to self. If a logger was previously registered it will receive the log.\n        const err = new Error(\n          'Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation'\n        );\n        self.error(err.stack ?? err.message);\n        return false;\n      }\n\n      if (typeof optionsOrLogLevel === 'number') {\n        optionsOrLogLevel = {\n          logLevel: optionsOrLogLevel,\n        };\n      }\n\n      const oldLogger = getGlobal('diag');\n      const newLogger = createLogLevelDiagLogger(\n        optionsOrLogLevel.logLevel ?? DiagLogLevel.INFO,\n        logger\n      );\n      // There already is an logger registered. We'll let it know before overwriting it.\n      if (oldLogger && !optionsOrLogLevel.suppressOverrideMessage) {\n        const stack = new Error().stack ?? '<failed to generate stacktrace>';\n        oldLogger.warn(`Current logger will be overwritten from ${stack}`);\n        newLogger.warn(\n          `Current logger will overwrite one already registered from ${stack}`\n        );\n      }\n\n      return registerGlobal('diag', newLogger, self, true);\n    };\n\n    self.setLogger = setLogger;\n\n    self.disable = () => {\n      unregisterGlobal(API_NAME, self);\n    };\n\n    self.createComponentLogger = (options: ComponentLoggerOptions) => {\n      return new DiagComponentLogger(options);\n    };\n\n    self.verbose = _logProxy('verbose');\n    self.debug = _logProxy('debug');\n    self.info = _logProxy('info');\n    self.warn = _logProxy('warn');\n    self.error = _logProxy('error');\n  }\n\n  public setLogger!: DiagLoggerApi['setLogger'];\n  /**\n   *\n   */\n  public createComponentLogger!: (\n    options: ComponentLoggerOptions\n  ) => DiagLogger;\n\n  // DiagLogger implementation\n  public verbose!: DiagLogFunction;\n  public debug!: DiagLogFunction;\n  public info!: DiagLogFunction;\n  public warn!: DiagLogFunction;\n  public error!: DiagLogFunction;\n\n  /**\n   * Unregister the global logger and return to Noop\n   */\n  public disable!: () => void;\n}\n"]}