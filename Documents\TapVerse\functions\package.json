{"name": "tapverse-casino-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for TapVerse Casino", "main": "src/index.js", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "jest": "^29.7.0", "firebase-functions-test": "^3.1.0"}, "private": true, "keywords": ["firebase", "functions", "casino", "games", "tapverse"], "author": "TapVerse Team", "license": "MIT"}