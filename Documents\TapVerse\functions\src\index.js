const { initializeApp } = require('firebase-admin/app');
const { setGlobalOptions } = require('firebase-functions/v2');

// Initialize Firebase Admin
initializeApp();

// Set global options for all functions
setGlobalOptions({
  region: 'us-central1',
  maxInstances: 100,
});

// Import casino functions
const spinWheel = require('./casino/spinWheel');
const highLow = require('./casino/highLow');
const luckyDarts = require('./casino/luckyDarts');

// Export casino functions
exports.spinWheel = spinWheel.spinWheel;
exports.validateSpinResult = spinWheel.validateSpinResult;

exports.playHighLow = highLow.playHighLow;
exports.startHighLow = highLow.startHighLow;

exports.playLuckyDarts = luckyDarts.playLuckyDarts;
exports.getDartZoneInfo = luckyDarts.getDartZoneInfo;

// Roll 'n' Risk functions
const { onCall, HttpsError } = require('firebase-functions/v2/https');
const { getFirestore, FieldValue } = require('firebase-admin/firestore');

const db = getFirestore();

const ROLL_RISK_ENTRY_COST = 30;
const ROLL_RISK_REWARDS = {
  2: 500,  // Jackpot
  3: 60, 4: 60, 5: 60, 6: 60,  // Small Win
  7: 30,   // Neutral (break even)
  8: 15, 9: 15, 10: 15,  // Small Loss
  11: 0, 12: 0,  // Full Loss
};

const ROLL_RISK_OUTCOMES = {
  2: 'JACKPOT!',
  3: 'Small Win', 4: 'Small Win', 5: 'Small Win', 6: 'Small Win',
  7: 'Break Even',
  8: 'Small Loss', 9: 'Small Loss', 10: 'Small Loss',
  11: 'Full Loss', 12: 'Full Loss',
};

exports.playRollNRisk = onCall(async (request) => {
  try {
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const uid = request.auth.uid;
    const userRef = db.collection('users').doc(uid);

    const result = await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new HttpsError('not-found', 'User document not found');
      }

      const userData = userDoc.data();
      const currentTokens = userData.tokens || 0;

      if (currentTokens < ROLL_RISK_ENTRY_COST) {
        throw new HttpsError('failed-precondition', 'Insufficient tokens');
      }

      // Roll dice
      const die1 = Math.floor(Math.random() * 6) + 1;
      const die2 = Math.floor(Math.random() * 6) + 1;
      const total = die1 + die2;
      
      const tokensWon = ROLL_RISK_REWARDS[total];
      const outcome = ROLL_RISK_OUTCOMES[total];
      const newTokenBalance = currentTokens - ROLL_RISK_ENTRY_COST + tokensWon;

      // Update user tokens
      transaction.update(userRef, {
        tokens: newTokenBalance,
        lastActivity: FieldValue.serverTimestamp(),
      });

      // Log transaction
      const transactionRef = userRef.collection('casinoTransactions').doc();
      transaction.set(transactionRef, {
        gameId: 'roll_n_risk',
        type: 'roll',
        entryCost: ROLL_RISK_ENTRY_COST,
        tokensWon: tokensWon,
        netGain: tokensWon - ROLL_RISK_ENTRY_COST,
        gameData: { die1, die2, total, outcome },
        timestamp: FieldValue.serverTimestamp(),
      });

      // Update stats
      const statsRef = userRef.collection('casinoStats').doc('roll_n_risk');
      const statsDoc = await transaction.get(statsRef);
      
      if (statsDoc.exists) {
        const stats = statsDoc.data();
        transaction.update(statsRef, {
          gamesPlayed: (stats.gamesPlayed || 0) + 1,
          wins: (stats.wins || 0) + (tokensWon >= ROLL_RISK_ENTRY_COST ? 1 : 0),
          losses: (stats.losses || 0) + (tokensWon < ROLL_RISK_ENTRY_COST ? 1 : 0),
          totalTokensWon: (stats.totalTokensWon || 0) + tokensWon,
          totalTokensSpent: (stats.totalTokensSpent || 0) + ROLL_RISK_ENTRY_COST,
          lastPlayed: FieldValue.serverTimestamp(),
          jackpots: (stats.jackpots || 0) + (total === 2 ? 1 : 0),
        });
      } else {
        transaction.set(statsRef, {
          gameId: 'roll_n_risk',
          gamesPlayed: 1,
          wins: tokensWon >= ROLL_RISK_ENTRY_COST ? 1 : 0,
          losses: tokensWon < ROLL_RISK_ENTRY_COST ? 1 : 0,
          totalTokensWon: tokensWon,
          totalTokensSpent: ROLL_RISK_ENTRY_COST,
          firstPlayed: FieldValue.serverTimestamp(),
          lastPlayed: FieldValue.serverTimestamp(),
          jackpots: total === 2 ? 1 : 0,
        });
      }

      return {
        success: true,
        die1, die2, total,
        outcome,
        tokensWon,
        newTokenBalance,
        entryCost: ROLL_RISK_ENTRY_COST,
      };
    });

    return result;

  } catch (error) {
    console.error('Error in playRollNRisk function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});

// Mystery Multiplier functions
const MYSTERY_ENTRY_COST = 100;
const MYSTERY_MULTIPLIERS = [0.0, 0.5, 1.0, 2.0, 3.0, 5.0];
const MYSTERY_PROBABILITIES = [0.30, 0.20, 0.20, 0.15, 0.10, 0.05];

function selectRandomMultiplier() {
  const random = Math.random();
  let cumulativeProbability = 0;

  for (let i = 0; i < MYSTERY_MULTIPLIERS.length; i++) {
    cumulativeProbability += MYSTERY_PROBABILITIES[i];
    if (random <= cumulativeProbability) {
      return MYSTERY_MULTIPLIERS[i];
    }
  }

  return 0.0; // Fallback
}

exports.playMysteryMultiplier = onCall(async (request) => {
  try {
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { boxChoice } = request.data;
    
    if (!boxChoice || ![1, 2, 3].includes(boxChoice)) {
      throw new HttpsError('invalid-argument', 'Invalid box choice. Must be 1, 2, or 3');
    }

    const uid = request.auth.uid;
    const userRef = db.collection('users').doc(uid);

    const result = await db.runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new HttpsError('not-found', 'User document not found');
      }

      const userData = userDoc.data();
      const currentTokens = userData.tokens || 0;

      if (currentTokens < MYSTERY_ENTRY_COST) {
        throw new HttpsError('failed-precondition', 'Insufficient tokens');
      }

      // Generate multipliers for all 3 boxes
      const multipliers = [
        selectRandomMultiplier(),
        selectRandomMultiplier(),
        selectRandomMultiplier(),
      ];
      
      const selectedMultiplier = multipliers[boxChoice - 1];
      const tokensWon = Math.round(MYSTERY_ENTRY_COST * selectedMultiplier);
      const newTokenBalance = currentTokens - MYSTERY_ENTRY_COST + tokensWon;

      // Update user tokens
      transaction.update(userRef, {
        tokens: newTokenBalance,
        lastActivity: FieldValue.serverTimestamp(),
      });

      // Log transaction
      const transactionRef = userRef.collection('casinoTransactions').doc();
      transaction.set(transactionRef, {
        gameId: 'mystery_multiplier',
        type: 'play',
        entryCost: MYSTERY_ENTRY_COST,
        tokensWon: tokensWon,
        netGain: tokensWon - MYSTERY_ENTRY_COST,
        gameData: { 
          boxChoice, 
          selectedMultiplier, 
          allMultipliers: multipliers 
        },
        timestamp: FieldValue.serverTimestamp(),
      });

      // Update stats
      const statsRef = userRef.collection('casinoStats').doc('mystery_multiplier');
      const statsDoc = await transaction.get(statsRef);
      
      if (statsDoc.exists) {
        const stats = statsDoc.data();
        transaction.update(statsRef, {
          gamesPlayed: (stats.gamesPlayed || 0) + 1,
          wins: (stats.wins || 0) + (tokensWon > MYSTERY_ENTRY_COST ? 1 : 0),
          losses: (stats.losses || 0) + (tokensWon < MYSTERY_ENTRY_COST ? 1 : 0),
          totalTokensWon: (stats.totalTokensWon || 0) + tokensWon,
          totalTokensSpent: (stats.totalTokensSpent || 0) + MYSTERY_ENTRY_COST,
          lastPlayed: FieldValue.serverTimestamp(),
        });
      } else {
        transaction.set(statsRef, {
          gameId: 'mystery_multiplier',
          gamesPlayed: 1,
          wins: tokensWon > MYSTERY_ENTRY_COST ? 1 : 0,
          losses: tokensWon < MYSTERY_ENTRY_COST ? 1 : 0,
          totalTokensWon: tokensWon,
          totalTokensSpent: MYSTERY_ENTRY_COST,
          firstPlayed: FieldValue.serverTimestamp(),
          lastPlayed: FieldValue.serverTimestamp(),
        });
      }

      return {
        success: true,
        boxChoice,
        selectedMultiplier,
        allMultipliers: multipliers,
        tokensWon,
        newTokenBalance,
        entryCost: MYSTERY_ENTRY_COST,
      };
    });

    return result;

  } catch (error) {
    console.error('Error in playMysteryMultiplier function:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'An internal error occurred');
  }
});
