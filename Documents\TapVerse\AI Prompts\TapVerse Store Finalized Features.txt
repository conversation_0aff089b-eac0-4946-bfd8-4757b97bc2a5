 TapVerse Store: Finalized Features & Structure
✅ Selected Features to Include
Feature	Description
1. Cosmetics	Skins for balls, paddles, backgrounds, etc. Visual-only, no gameplay impact.
2. Game Unlocks	Some games require tokens to unlock, others are free from the start.
5. Leaderboard Flair	Custom badges or nameplate frames on leaderboards.
7. Mystery Draws	Spend tokens for a random cosmetic, flair, or token prize.
8. Token Boosts	Limited-time token multiplier boosts (24h, 3 games, etc).

📁 Firestore Structure
plaintext
Copy
Edit
store/
  items/
    [item_id] {
      name: "Neon Ball",
      type: "cosmetic",
      subtype: "ball_skin",
      price: 150,
      rarity: "rare",
      image: "neon_ball.png"
    }

    [item_id] {
      name: "Elite Frame",
      type: "flair",
      subtype: "leaderboard_frame",
      price: 250,
      rarity: "epic"
    }

    [item_id] {
      name: "Mystery Crate",
      type: "mystery_draw",
      price: 100,
      contents: ["item_ref1", "item_ref2", "tokens_50", "tokens_100"]
    }

  boosts/
    daily_boost {
      type: "token_multiplier",
      duration: 24, // in hours
      multiplier: 2,
      price: 200
    }

users/{uid}/
  ownedItems/ [item_id]: true
  activeFlair: item_id
  activeSkins: {
    ball_skin: item_id,
    paddle_skin: item_id,
  }
  boosts/activeBoost {
    multiplier: 2,
    expiresAt: timestamp
  }
🎮 Store Item Categories & UX Details
🎨 1. Cosmetics (Visual Skins)
Ball skins (used in Basketball Flick, Pong, Brick Breaker)

Paddle skins (Pong, Brick Breaker)

Background themes

Runner avatars or color trails

✅ Users can preview these in a carousel.
🧠 Tokens required: 75–300, tiered by rarity (common, rare, epic).

🔓 2. Game Unlocks
Start with 6–8 free games.

Unlock additional games using tokens.

🧠 Encourage unlocking through high score goals or token savings.
Cost per unlock: 100–400 tokens depending on popularity/complexity.

🏆 5. Leaderboard Flair
Nameplate colors or decorative borders

Trophy icons, VIP badges, or seasonal flair

🎨 Shown next to username on leaderboards.
Cost: 150–400 tokens
Optional: Only top players can unlock certain rare flairs.

🎁 7. Mystery Draws
"Mystery Crate" for 100 tokens.

Random reward from:

A rare or epic skin

Leaderboard flair

Token reward (e.g., +50 tokens, +100 tokens)

🧠 Chance-based dopamine loop. Use lootbox-style animation and sound FX.

🔁 8. Token Boosts
2× token multiplier for 3 games or 24 hours

Daily limit on use to preserve balance

Cost: 150–250 tokens
Active state tracked in users/{uid}/boosts

🧭 User Flow (Frontend)
Tap Store tab from home screen.

View categories:

“Cosmetics”

“Unlock Games”

“Leaderboard Flair”

“Mystery Crate”

“Token Boosts”

Tap item → View preview modal → Confirm purchase.

On purchase:

Deduct tokens from users/{uid}/tokens

Add item to users/{uid}/ownedItems

Apply if equipped.

🖼️ UI Design Tips (Flutter)
Use GridView with clear rarity indicators (colors: grey, blue, purple, gold).

Modal for previewing cosmetics (3D or animated widget).

Add small token icon overlay for prices.

Use shimmer or animation on new/unlocked items.

Apply flutter_animate or rive for skin previews and crate opens.

🧪 Token Pricing Balancing (Sample)
Item Type	Rarity	Cost (Tokens)
Ball Skin	Common	75
Paddle Skin	Rare	150
Background Theme	Epic	250
Game Unlock	Rare	200–400
Flair Frame	Epic	300
Token Boost	N/A	200
Mystery Crate	N/A	100